#!/usr/bin/env python3
"""
企业微信机器人Markdown消息Attachment使用示例

本示例展示如何使用RspMarkdownMsg类创建带有按钮的markdown消息
"""

from rsp_msg import RspMarkdownMsg


def example_basic_attachment():
    """基本的attachment使用示例"""
    print("=== 基本Attachment使用示例 ===")
    
    # 创建markdown消息
    msg = RspMarkdownMsg()
    msg.content = "**请选择你的尺码**\n\n主题：2019文化衫尺码收集\n范围：所有正式员工+实习生\n服装：统一为蓝色logo+白色T"
    msg.at_short_name = True
    
    # 手动添加attachment
    actions = [
        {
            'name': 'button_s',
            'text': 'S',
            'type': 'button',
            'value': 'S',
            'replace_text': '你已选择S',
            'border_color': '#2EAB49',
            'text_color': '#2EAB49'
        },
        {
            'name': 'button_m',
            'text': 'M',
            'type': 'button',
            'value': 'M',
            'replace_text': '你已选择M',
            'border_color': '#2EAB49',
            'text_color': '#2EAB49'
        },
        {
            'name': 'button_l',
            'text': 'L',
            'type': 'button',
            'value': 'L',
            'replace_text': '你已选择L',
            'border_color': '#2EAB49',
            'text_color': '#2EAB49'
        }
    ]
    
    msg.add_attachment('size_selection', actions)
    
    # 生成XML
    xml_output = msg.dump_xml()
    print("生成的XML:")
    print(xml_output.decode())
    print()


def example_convenient_method():
    """使用便捷方法创建带按钮的消息"""
    print("=== 便捷方法使用示例 ===")
    
    content = "**投票：你最喜欢的编程语言**\n\n请选择你最喜欢的编程语言："
    
    button_configs = [
        {
            'name': 'python_btn',
            'text': 'Python',
            'value': 'python',
            'replace_text': '你选择了Python',
            'border_color': '#3776ab',
            'text_color': '#3776ab'
        },
        {
            'name': 'java_btn',
            'text': 'Java',
            'value': 'java',
            'replace_text': '你选择了Java',
            'border_color': '#f89820',
            'text_color': '#f89820'
        },
        {
            'name': 'go_btn',
            'text': 'Go',
            'value': 'go',
            'replace_text': '你选择了Go',
            'border_color': '#00add8',
            'text_color': '#00add8'
        },
        {
            'name': 'other_btn',
            'text': '其他',
            'value': 'other',
            'replace_text': '你选择了其他语言'
        }
    ]
    
    msg = RspMarkdownMsg.create_with_buttons(
        content=content,
        callback_id='language_vote',
        button_configs=button_configs,
        at_short_name=True
    )
    
    # 生成XML
    xml_output = msg.dump_xml()
    print("生成的XML:")
    print(xml_output.decode())
    print()


def example_multiple_attachments():
    """多个attachment的示例"""
    print("=== 多个Attachment示例 ===")
    
    msg = RspMarkdownMsg()
    msg.content = "**会议安排确认**\n\n请确认以下信息：\n1. 会议时间\n2. 参会方式"
    
    # 第一个attachment：时间选择
    time_actions = [
        {'name': 'time_9am', 'text': '上午9点', 'value': '09:00', 'replace_text': '已选择上午9点'},
        {'name': 'time_2pm', 'text': '下午2点', 'value': '14:00', 'replace_text': '已选择下午2点'},
        {'name': 'time_4pm', 'text': '下午4点', 'value': '16:00', 'replace_text': '已选择下午4点'}
    ]
    msg.add_attachment('time_selection', time_actions)
    
    # 第二个attachment：参会方式
    method_actions = [
        {'name': 'online', 'text': '线上参会', 'value': 'online', 'replace_text': '选择线上参会'},
        {'name': 'offline', 'text': '现场参会', 'value': 'offline', 'replace_text': '选择现场参会'}
    ]
    msg.add_attachment('method_selection', method_actions)
    
    # 生成XML
    xml_output = msg.dump_xml()
    print("生成的XML:")
    print(xml_output.decode())
    print()


def example_dynamic_buttons():
    """动态添加按钮的示例"""
    print("=== 动态添加按钮示例 ===")
    
    msg = RspMarkdownMsg()
    msg.content = "**评分系统**\n\n请为本次服务打分（1-5星）："
    
    # 先添加一个空的attachment
    msg.add_attachment('rating_system', [])
    
    # 动态添加按钮
    for i in range(1, 6):
        stars = '⭐' * i
        msg.add_button_action(
            attachment_index=0,
            name=f'star_{i}',
            text=f'{stars} ({i}星)',
            value=str(i),
            replace_text=f'你给出了{i}星评价',
            border_color='#FFD700',
            text_color='#FFD700'
        )
    
    # 生成XML
    xml_output = msg.dump_xml()
    print("生成的XML:")
    print(xml_output.decode())
    print()


def example_json_format():
    """展示对应的JSON格式（用于主动发送消息）"""
    print("=== 对应的JSON格式示例 ===")
    
    # 这是用于主动发送消息的JSON格式示例
    json_example = {
        "chatid": "wrkSFfCgAAtMQKg4xqDatM5C9IDHFpTw",
        "msgtype": "markdown",
        "markdown": {
            "content": "**2019公司文化衫尺码收集**\n\n主题：2019文化衫尺码收集\n范围：所有正式员工+实习生\n服装：统一为蓝色logo+白色T\n\n请选择你需要的尺码",
            "at_short_name": True,
            "attachments": [{
                "callback_id": "button_size_selection",
                "actions": [
                    {
                        "name": "button_s",
                        "text": "S",
                        "type": "button",
                        "value": "S",
                        "replace_text": "你已选择S",
                        "border_color": "#2EAB49",
                        "text_color": "#2EAB49"
                    },
                    {
                        "name": "button_m",
                        "text": "M",
                        "type": "button",
                        "value": "M",
                        "replace_text": "你已选择M",
                        "border_color": "#2EAB49",
                        "text_color": "#2EAB49"
                    },
                    {
                        "name": "button_l",
                        "text": "L",
                        "type": "button",
                        "value": "L",
                        "replace_text": "你已选择L",
                        "border_color": "#2EAB49",
                        "text_color": "#2EAB49"
                    }
                ]
            }]
        }
    }
    
    import json
    print("对应的JSON格式（用于主动发送消息）:")
    print(json.dumps(json_example, indent=2, ensure_ascii=False))
    print()


if __name__ == "__main__":
    print("企业微信机器人Markdown Attachment使用示例\n")
    
    example_basic_attachment()
    example_convenient_method()
    example_multiple_attachments()
    example_dynamic_buttons()
    example_json_format()
    
    print("所有示例运行完成！")
