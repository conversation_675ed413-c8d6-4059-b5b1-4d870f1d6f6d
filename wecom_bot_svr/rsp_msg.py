import xml.etree.ElementTree as ET

# https://developer.work.weixin.qq.com/document/path/99399#%E5%8A%A0%E5%AF%86%E4%B8%8E%E5%9B%9E%E5%A4%8D

class RspMsg(object):
    def __init__(self):
        self.msg_type = None
        self.visible_to_user = None
        self.xml_tree = ET.Element('xml')

    def insert_elem(self, name, value):
        curr_node = self.xml_tree

        for elem_name in name.split('/'):
            if curr_node.find(elem_name) is None:
                e = ET.Element(elem_name)
                curr_node.append(e)
            curr_node = curr_node.find(elem_name)
        curr_node.text = value

    def dump_xml(self):
        self.update_xml()
        return ET.tostring(self.xml_tree, encoding='utf-8', method='xml')


    def update_xml(self):
        self.insert_elem("MsgType", self.msg_type)
        if self.visible_to_user is not None:
            self.insert_elem("VisibleToUser", "|".join([str(x) for x in self.visible_to_user]))


class RspTextMsg(RspMsg):
    def __init__(self):
        super().__init__()
        self.msg_type = 'text'
        self.content = None

    def update_xml(self):
        super().update_xml()
        self.insert_elem('Text/Content', self.content)


class RspMarkdownMsg(RspMsg):
    def __init__(self):
        super().__init__()
        self.msg_type = 'markdown'
        self.content = None
        self.at_short_name = None
        self.attachments = None

    def update_xml(self):
        super().update_xml()
        self.insert_elem('Markdown/Content', self.content)
        if self.at_short_name is not None:
            self.insert_elem('Markdown/AtShortName', str(self.at_short_name).lower())
        if self.attachments is not None:
            self._add_attachments_to_xml()

    def _add_attachments_to_xml(self):
        """添加attachments到XML结构中"""
        if not self.attachments:
            return

        for i, attachment in enumerate(self.attachments):
            attachment_path = f'Markdown/Attachments/Attachment{i+1}'

            # 添加callback_id
            if 'callback_id' in attachment:
                self.insert_elem(f'{attachment_path}/CallbackId', attachment['callback_id'])

            # 添加actions
            if 'actions' in attachment:
                for j, action in enumerate(attachment['actions']):
                    action_path = f'{attachment_path}/Actions/Action{j+1}'

                    # 必填字段
                    if 'name' in action:
                        self.insert_elem(f'{action_path}/Name', action['name'])
                    if 'text' in action:
                        self.insert_elem(f'{action_path}/Text', action['text'])
                    if 'type' in action:
                        self.insert_elem(f'{action_path}/Type', action['type'])
                    if 'value' in action:
                        self.insert_elem(f'{action_path}/Value', action['value'])
                    if 'replace_text' in action:
                        self.insert_elem(f'{action_path}/ReplaceText', action['replace_text'])

                    # 可选字段
                    if 'border_color' in action:
                        self.insert_elem(f'{action_path}/BorderColor', action['border_color'])
                    if 'text_color' in action:
                        self.insert_elem(f'{action_path}/TextColor', action['text_color'])

    def add_attachment(self, callback_id, actions):
        """
        添加一个attachment

        Args:
            callback_id (str): attachment对应的回调id
            actions (list): action列表，每个action是一个字典包含name, text, type, value, replace_text等字段
        """
        if self.attachments is None:
            self.attachments = []

        attachment = {
            'callback_id': callback_id,
            'actions': actions
        }
        self.attachments.append(attachment)

    def add_button_action(self, attachment_index, name, text, value, replace_text,
                         action_type='button', border_color=None, text_color=None):
        """
        为指定的attachment添加一个按钮action

        Args:
            attachment_index (int): attachment的索引
            name (str): action名字
            text (str): 按钮显示文本
            value (str): action的值
            replace_text (str): 点击后替换的文本
            action_type (str): action类型，默认为'button'
            border_color (str): 按钮边框颜色，可选
            text_color (str): 按钮文字颜色，可选
        """
        if self.attachments is None or attachment_index >= len(self.attachments):
            raise IndexError("Attachment index out of range")

        action = {
            'name': name,
            'text': text,
            'type': action_type,
            'value': value,
            'replace_text': replace_text
        }

        if border_color:
            action['border_color'] = border_color
        if text_color:
            action['text_color'] = text_color

        self.attachments[attachment_index]['actions'].append(action)

    @classmethod
    def create_with_buttons(cls, content, callback_id, button_configs, at_short_name=None):
        """
        创建带有按钮的markdown消息的便捷方法

        Args:
            content (str): markdown内容
            callback_id (str): attachment的回调id
            button_configs (list): 按钮配置列表，每个元素是包含按钮信息的字典
                例如: [
                    {'name': 'btn1', 'text': 'S', 'value': 'S', 'replace_text': '你已选择S'},
                    {'name': 'btn2', 'text': 'M', 'value': 'M', 'replace_text': '你已选择M'}
                ]
            at_short_name (bool): 是否使用短名字@人，可选

        Returns:
            RspMarkdownMsg: 配置好的markdown消息对象
        """
        msg = cls()
        msg.content = content
        msg.at_short_name = at_short_name

        # 创建actions列表
        actions = []
        for btn_config in button_configs:
            action = {
                'name': btn_config['name'],
                'text': btn_config['text'],
                'type': btn_config.get('type', 'button'),
                'value': btn_config['value'],
                'replace_text': btn_config['replace_text']
            }

            # 添加可选的颜色配置
            if 'border_color' in btn_config:
                action['border_color'] = btn_config['border_color']
            if 'text_color' in btn_config:
                action['text_color'] = btn_config['text_color']

            actions.append(action)

        # 添加attachment
        msg.add_attachment(callback_id, actions)
        return msg
