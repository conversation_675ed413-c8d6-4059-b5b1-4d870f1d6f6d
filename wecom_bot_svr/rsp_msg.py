import xml.etree.cElementTree as ET

# https://developer.work.weixin.qq.com/document/path/99399#%E5%8A%A0%E5%AF%86%E4%B8%8E%E5%9B%9E%E5%A4%8D

class RspMsg(object):
    def __init__(self):
        self.msg_type = None
        self.visible_to_user = None
        self.xml_tree = ET.Element('xml')

    def insert_elem(self, name, value):
        curr_node = self.xml_tree

        for n in name.split('/'):
            if curr_node.find(n) is None:
                e = ET.Element(n)
                curr_node.append(e)
            curr_node = curr_node.find(n)
        curr_node.text = value

    def dump_xml(self):
        self.update_xml()
        return ET.tostring(self.xml_tree, encoding='ascii', method='html')

    def update_xml(self):
        self.insert_elem("MsgType", self.msg_type)
        if self.visible_to_user is not None:
            self.insert_elem("VisibleToUser", "|".join([str(x) for x in self.visible_to_user]))


class RspTextMsg(RspMsg):
    def __init__(self):
        super().__init__()
        self.msg_type = 'text'
        self.content = None

    def update_xml(self):
        super().update_xml()
        self.insert_elem('Text/Content', self.content)


class RspMarkdownMsg(RspMsg):
    def __init__(self):
        super().__init__()
        self.msg_type = 'markdown'
        self.content = None
        self.attachments = None

    def update_xml(self):
        super().update_xml()
        self.insert_elem('Markdown/Content', self.content)


class MessageXMLBuilder:
    def __init__(self):
        self.msg_type = None
        self.visible_to_user = None
        self.attachments = None
        self.xml_tree = ET.Element('xml')

    def insert_elem(self, name, value):
        curr_node = self.xml_tree
        for n in name.split('/'):
            if curr_node.find(n) is None:
                e = ET.Element(n)
                curr_node.append(e)
            curr_node = curr_node.find(n)
        curr_node.text = value

    def insert_attachments(self, parent_node, attachments):
        """
        递归把 attachments（列表或字典）转成 XML 节点
        """
        if isinstance(attachments, list):
            for i, item in enumerate(attachments):
                # 用统一的节点名，比如 Attachment 或 Item
                item_node = ET.SubElement(parent_node, 'Attachment')
                self.insert_attachments(item_node, item)
        elif isinstance(attachments, dict):
            for key, val in attachments.items():
                child = ET.SubElement(parent_node, key)
                if isinstance(val, (dict, list)):
                    self.insert_attachments(child, val)
                else:
                    child.text = str(val)
        else:
            # 其他类型直接转文本
            parent_node.text = str(attachments)

    def update_xml(self):
        self.insert_elem("MsgType", self.msg_type if self.msg_type else "")
        if self.visible_to_user is not None:
            if isinstance(self.visible_to_user, (list, tuple)):
                visible_str = "|".join([str(x) for x in self.visible_to_user])
            else:
                visible_str = str(self.visible_to_user)
            self.insert_elem("VisibleToUser", visible_str)

        # 处理 attachments
        if self.attachments is not None:
            # 先找或创建 Attachments 节点
            attachments_node = self.xml_tree.find('Attachments')
            if attachments_node is None:
                attachments_node = ET.SubElement(self.xml_tree, 'Attachments')
            # 清空旧内容
            attachments_node.clear()
            # 插入新的 attachments 内容
            self.insert_attachments(attachments_node, self.attachments)

    def dump_xml(self):
        self.update_xml()
        return ET.tostring(self.xml_tree, encoding='ascii', method='html')


# 测试示例
if __name__ == "__main__":
    builder = RspMsg()
    builder.msg_type = "markdown"
    builder.visible_to_user = ["zhangsan"]
    builder.attachments = [{
        "callback_id": "button_two_row",
        "actions": [
            {
                "name": "button_1",
                "text": "S",
                "type": "button",
                "value": "S",
                "replace_text": "你已选择S",
                "border_color": "#2EAB49",
                "text_color": "#2EAB49"
            },
            {
                "name": "button_2",
                "text": "M",
                "type": "button",
                "value": "M",
                "replace_text": "你已选择M",
                "border_color": "#2EAB49",
                "text_color": "#2EAB49"
            }
        ]
    }]

    xml_str = builder.dump_xml()
    print(xml_str)
