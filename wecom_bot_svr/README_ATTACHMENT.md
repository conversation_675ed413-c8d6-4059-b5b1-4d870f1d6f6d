# 企业微信机器人 Markdown Attachment 功能

## 概述

本功能为企业微信机器人的 Markdown 消息添加了 attachment（按钮）支持，允许创建带有交互按钮的消息。

## 功能特性

- ✅ 支持在 Markdown 消息中添加按钮
- ✅ 支持多个 attachment
- ✅ 支持按钮颜色自定义
- ✅ 支持 @人功能
- ✅ 提供便捷的创建方法
- ✅ 兼容现有的消息处理流程

## 使用方法

### 1. 基本用法

```python
from rsp_msg import RspMarkdownMsg

# 创建带按钮的 Markdown 消息
msg = RspMarkdownMsg()
msg.content = "**请选择你的尺码**\n\n请选择合适的尺码："
msg.at_short_name = True

# 添加按钮
actions = [
    {
        'name': 'button_s',
        'text': 'S',
        'type': 'button',
        'value': 'S',
        'replace_text': '你已选择S',
        'border_color': '#2EAB49',
        'text_color': '#2EAB49'
    },
    {
        'name': 'button_m',
        'text': 'M',
        'type': 'button',
        'value': 'M',
        'replace_text': '你已选择M',
        'border_color': '#2EAB49',
        'text_color': '#2EAB49'
    }
]

msg.add_attachment('size_selection', actions)
```

### 2. 便捷方法

```python
# 使用便捷方法创建
button_configs = [
    {'name': 'yes', 'text': '同意', 'value': 'yes', 'replace_text': '你选择了同意'},
    {'name': 'no', 'text': '拒绝', 'value': 'no', 'replace_text': '你选择了拒绝'}
]

msg = RspMarkdownMsg.create_with_buttons(
    content="**请确认**\n\n是否同意此提案？",
    callback_id='approval_request',
    button_configs=button_configs,
    at_short_name=True
)
```

### 3. 动态添加按钮

```python
msg = RspMarkdownMsg()
msg.content = "**评分**\n\n请为服务打分："

# 先添加空的 attachment
msg.add_attachment('rating', [])

# 动态添加按钮
for i in range(1, 6):
    msg.add_button_action(
        attachment_index=0,
        name=f'star_{i}',
        text=f'{i}星',
        value=str(i),
        replace_text=f'你给出了{i}星评价'
    )
```

## 主动发送消息

对于主动发送的消息，`app.py` 中的 `send_markdown` 方法已经支持 attachments 参数：

```python
# 通过 HTTP 接口发送
POST /active_send
{
    "msg_type": "markdown",
    "chat_id": "your_chat_id",
    "content": "**投票**\n\n请选择：",
    "attachments": [
        {
            "callback_id": "vote_callback",
            "actions": [
                {
                    "name": "option_a",
                    "text": "选项A",
                    "type": "button",
                    "value": "A",
                    "replace_text": "你选择了A"
                }
            ]
        }
    ]
}
```

## 按钮参数说明

### Attachment 参数
- `callback_id` (必填): 回调ID，用于识别按钮点击事件
- `actions` (必填): 按钮列表

### Action 参数
- `name` (必填): 按钮名称，回调时会传递此值
- `text` (必填): 按钮显示文本
- `type` (必填): 按钮类型，通常为 "button"
- `value` (必填): 按钮值，回调时会传递此值
- `replace_text` (必填): 点击后替换的文本
- `border_color` (可选): 按钮边框颜色
- `text_color` (可选): 按钮文字颜色

## 回调处理

当用户点击按钮时，会收到 `AttachmentReqMsg` 类型的消息：

```python
def handle_message(msg, bot_server):
    if msg.msg_type == 'attachment':
        callback_id = msg.callback_id
        action = msg.actions[0]  # 获取第一个action
        action_name = action.name
        action_value = action.value
        
        # 处理按钮点击逻辑
        return create_response_message(f"收到按钮点击: {action_value}")
```

## 注意事项

1. 每个 attachment 最多支持 20 个 action
2. 按钮文本最长 128 字节
3. 按钮名称最长 64 字节
4. 替换文本最长 128 字节
5. XML 序列化时 `Name` 标签会被缩写为 `n`，这是正常现象，不影响功能

## 示例文件

查看 `example_attachment_usage.py` 获取更多详细示例。
