
群机器人接口，markdown类型 数据格式

{
    "chatid": "wrkSFfCgAAtMQKg4xqDatM5C9IDHFpTw",
	"post_id" : "bpkSFfCgAAWeiHNo2p6lJbG3_F2xxxxx",
	"msgtype": "markdown",
	"visible_to_user": "zhangsan",
	"markdown": {
		"content": "**2019公司文化衫尺码收集**\n\n主题：2019文化衫尺码收集\n范围：所有<font color=\"warning\">正式员工+实习生</font>\n服装：统一为蓝色logo+白色T\n\n请选择你需要的尺码\n<@zhangsan><@lisi>",
		"at_short_name" : true,
		"attachments": [{
			"callback_id": "button_two_row",
			"actions": [{
					"name": "button_1",
					"text": "S",
					"type": "button",
					"value": "S",
					"replace_text": "你已选择S",
					"border_color": "#2EAB49",
					"text_color": "#2EAB49"
				},
				{
					"name": "button_2",
					"text": "M",
					"type": "button",
					"value": "M",
					"replace_text": "你已选择M",
					"border_color": "#2EAB49",
					"text_color": "#2EAB49"
				},
				{
					"name": "button_3",
					"text": "L",
					"type": "button",
					"value": "L",
					"replace_text": "你已选择L",
					"border_color": "#2EAB49",
					"text_color": "#2EAB49"
				},
				{
					"name": "button_4",
					"text": "不确定",
					"type": "button",
					"value": "不确定",
					"replace_text": "你已选择不确定",
					"border_color": "#2EAB49",
					"text_color": "#2EAB49"
				},
				{
					"name": "button_5",
					"text": "不参加",
					"type": "button",
					"value": "不参加",
					"replace_text": "你已选择不参加",
					"border_color": "#2EAB49",
					"text_color": "#2EAB49"
				}
			]
		}
		]
	}
}


参数	是否必填	说明
chatid	否	会话id，支持最多传100个，用‘|’分隔。可能是群聊会话，也可能是单聊会话或者小黑板会话，通过消息回调获得，也可以是userid。 特殊的，当chatid为“@all_group”时，表示对所有群广播，为“@all_blackboard”时，表示对所有小黑板广播，为“@all”时，表示对所有群和所有小黑板广播。不填则默认为“@all_group”。
post_id	否	小黑板帖子id，有且只有chatid指定了一个小黑板的时候生效
msgtype	是	消息类型，此时固定为markdown
content	是	markdown内容，最长不超过4096个字节，必须是utf8编码
at_short_name	否	markdown内容中@人指定为的短名字的方式，类型为bool值，设置为true则markdown中@xxx的表现为短名
attachments	否	attachments内容，目前仅支持button类型
visible_to_user	否	该消息只有指定的群成员或小黑板成员可见（其他成员不可见），有且只有chatid指定了一个群或一个小黑板的时候生效，多个userid用‘|’分隔



- attachments的参数说明：
参数	是否必填	说明
callback_id	是	attachments对应的回调id，企业微信在回调时会透传该值
actions	是	attachments动作， 一个attachment中最多支持20个action
type	是	action类型，目前仅支持按钮
name	是	action名字，企业微信回调时会透传该值， 最长不超过64字节。为了区分不同的按钮，建议开发者自行保证name的唯一性
text	是	需要展示的文本，最长不超过128字节
border_color	否	按钮边框颜色
text_color	否	按钮文字颜色
value	是	action的值，企业微信回调时会透传该值，最长不超过128字节
replace_text	是	点击按钮后替换的文本，最长不超过128字节
