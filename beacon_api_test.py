import hashlib
import time
import requests


# 参考文档：https://doc.beacon.woa.com/page/openapi?app=%E6%95%B0%E6%8D%AE%E6%9F%A5%E8%AF%A2&interface=getCardAnalysisResultUsingGET
# API申请指南：https://iwiki.woa.com/p/4007758100

class BeaconAPIClient:
    def __init__(self, application_id: int, secret_key: str):
        self.application_id = application_id
        self.secret_key = secret_key
        self.session = requests.Session()
        self.sign_version = 'v1'
        self.base_url = "https://api.beacon.woa.com/openapi"

    def _get_sign(self):
        """
        生成签名和时间戳
        签名算法: sha256(sha256(secretKey + '-' + timestamp) + '-' + appId)
        timestamp为13位毫秒时间戳
        """
        timestamp = str(int(time.time() * 1000))
        key_hl_1 = hashlib.sha256()
        key_hl_1.update(f"{self.secret_key}-{timestamp}".encode('utf-8'))
        secret_key_sha256 = key_hl_1.hexdigest()

        key_hl_2 = hashlib.sha256()
        key_hl_2.update(f"{secret_key_sha256}-{self.application_id}".encode('utf-8'))
        sign = key_hl_2.hexdigest()

        return timestamp, sign

    def _get_headers(self):
        timestamp, sign = self._get_sign()
        headers = {
            "bc-api-app-id": str(self.application_id),
            "bc-api-sign-version": self.sign_version,
            "bc-api-timestamp": timestamp,
            "bc-api-sign": sign,
            "Content-Type": "application/x-www-form-urlencoded"
        }
        return headers

    def get_card_result(self, **query_params):
        """
        查询指定图卡的数据

        :param query_params: 支持接口文档中所有query参数，如 async=1, cardId=123 等
        :return: requests.Response对象
        """
        url = f"{self.base_url}/datainsight/analysis/getCardResult"
        headers = self._get_headers()

        # GET请求，参数放在params中
        response = self.session.get(url, headers=headers, params=query_params)
        return response
    
    def post_card_result(self, biz_id: str, card_query: dict):
        """
        查询指仪表盘的数据

        :param biz_id: 空间ID，必填，作为query参数
        :param card_query: 请求体JSON，必填，符合接口文档结构
        :return: requests.Response对象
        """
        url = f"{self.base_url}/datatalk/dataQuery/card/result"
        headers = self._get_headers()
        headers["Content-Type"] = "application/json"

        params = {
            "bizId": biz_id
        }

        response = self.session.post(url, headers=headers, params=params, json=card_query)
        return response
    


if __name__ == "__main__":
    APPLICATIONID = 2235
    SECRETKEY = "B40FF43E210D1B6363D3CA37DA2770B4"

    client = BeaconAPIClient(APPLICATIONID, SECRETKEY)

    # 查询指定图卡的数据
    params = {
        "async": 1, 
        "cardId": 608812,  # 图卡id，https://beacon.woa.com/datainsight/yyb_bi/Analytics_Mode/107398/Hue_Card_Modify/608812
        "bizId":"yyb_bi"  # 空间id yyb_bi
        }
    
    response = client.get_card_result(**params)

    print("状态码:", response.status_code)
    print("响应内容:", response.text)


    # 调用查询指定仪表盘的数据
    biz_id = "yyb_bi"  
    card_query = {
        "cacheStrategy": "FORCE",
        "cardId": "table_gg0nvee5",
        "limit": 200,
        "pageId": 239580,
        "paramsId": "7d118b20ee228ab45fc85dbd754fb643",
        "pushType": 0,
        "specialParam": {},
        "variables": [
              {
                  "key": "version",
                  "value": "'TMAF_858_P_9521','TMAF_858_P_9522','TMAF_858_P_9523','TMAF_857_P_9524','TMAF_858_P_9527','TMAF_858_P_9528','TMAF_858_P_9529','TMAF_857_P_9530'"
              },
              {
                  "key": "imp_date.start",
                  "value": 20250529
              },
              {
                  "key": "imp_date.end",
                  "value": 20250604
              }
        ]
    }
    response2 = client.post_card_result(biz_id=biz_id, card_query=card_query)
    print("post_card_result状态码:", response2.status_code)
    print("post_card_result响应内容:", response2.text)
