import hashlib
import time
import requests
import uuid


# 参考文档：https://doc.beacon.woa.com/page/openapi?app=%E6%95%B0%E6%8D%AE%E6%9F%A5%E8%AF%A2&interface=getCardAnalysisResultUsingGET
# API申请指南：https://iwiki.woa.com/p/4007758100


class BeaconAPIClient:
    def __init__(self, application_id: int, secret_key: str):
        self.application_id = application_id
        self.secret_key = secret_key
        self.session = requests.Session()
        self.sign_version = 'v1'
        self.base_url = "https://api.beacon.woa.com/openapi"

    def _get_sign(self):
        """
        生成签名和时间戳
        签名算法: sha256(sha256(secretKey + '-' + timestamp) + '-' + appId)
        timestamp为13位毫秒时间戳
        """
        timestamp = str(int(time.time() * 1000))
        key_hl_1 = hashlib.sha256()
        key_hl_1.update(f"{self.secret_key}-{timestamp}".encode('utf-8'))
        secret_key_sha256 = key_hl_1.hexdigest()

        key_hl_2 = hashlib.sha256()
        key_hl_2.update(f"{secret_key_sha256}-{self.application_id}".encode('utf-8'))
        sign = key_hl_2.hexdigest()

        return timestamp, sign

    def _get_headers(self, content_type="application/x-www-form-urlencoded"):
        timestamp, sign = self._get_sign()
        headers = {
            "bc-api-app-id": str(self.application_id),
            "bc-api-sign-version": self.sign_version,
            "bc-api-timestamp": timestamp,
            "bc-api-sign": sign,
            "Content-Type": content_type
        }
        return headers

    def get_card_result(self, **query_params):
        """
        查询指定图卡的数据

        :param query_params: 支持接口文档中所有query参数，如 async=1, cardId=123 等
        :return: requests.Response对象
        """
        url = f"{self.base_url}/datainsight/analysis/getCardResult"
        headers = self._get_headers()

        # GET请求，参数放在params中
        response = self.session.get(url, headers=headers, params=query_params)
        return response

    def post_card_result(self, biz_id: str, card_query: dict):
        """
        查询指定仪表盘的数据

        :param biz_id: 空间ID，必填，作为query参数
        :param card_query: 请求体JSON，必填，符合接口文档结构
        :return: requests.Response对象
        """
        url = f"{self.base_url}/datatalk/dataQuery/card/result"
        headers = self._get_headers(content_type="application/json")

        params = {
            "bizId": biz_id
        }

        response = self.session.post(url, headers=headers, params=params, json=card_query)
        return response

    def post_model_query(self, body: dict):
        """
        模型查询接口，直接传入完整请求体

        :param body: 请求体完整字典
        :return: requests.Response对象
        """
        url = f"{self.base_url}/datatalk/analysis/model?bizId=yyb_bi"
        headers = self._get_headers(content_type="application/json")

        response = self.session.post(url, headers=headers, json=body)
        return response

    def query_download_install_cvr(self, start_date: str, end_date: str,
                                   qua_big_versions: list = None,
                                   quas: list = None,
                                   brands: list = None,
                                   package_names: list = None,
                                   app_names: list = None,
                                   appids: list = None,
                                   data_source_id: int = 1235751):
        """
        查询下载安装CVR数据，基于install_download_sql.sql的完整逻辑

        :param start_date: 开始日期，格式如 '20250529'
        :param end_date: 结束日期，格式如 '20250604'
        :param qua_big_versions: QUA大版本号列表，如 ['858', '857']，None表示查询所有
        :param quas: QUA版本号列表，None表示查询所有
        :param brands: 品牌列表，如 ['huawei', 'xiaomi']，None表示查询所有
        :param package_names: 包名列表，None表示查询所有
        :param app_names: 应用名列表，None表示查询所有
        :param appids: 应用ID列表，None表示查询所有
        :param data_source_id: 数据源ID，默认1235751
        :return: requests.Response对象
        """

        # 构建参数字符串的辅助函数
        def build_param_list(param_list, param_name):
            if param_list is None:
                return "'total'"
            return "'" + "','".join(str(p) for p in param_list) + "'"

        # 构建各种参数
        select_qua_big_version = build_param_list(qua_big_versions, 'qua_big_version')
        select_qua = build_param_list(quas, 'qua')
        select_brand = build_param_list(brands, 'brand')
        select_package_name = build_param_list(package_names, 'package_name')
        select_app_name = build_param_list(app_names, 'app_name')
        select_appid = build_param_list(appids, 'appid')

        # 其他固定参数设为total（查询所有）
        fixed_total_params = "'total'"

        # 构建完整的SQL查询，基于install_download_sql.sql
        sql = f"""
select
ds as 日期
, case when 'total' in ({select_qua_big_version}) then '合计' else qua_big_version end as 大版本号
, case when 'total' in ({select_qua}) then '合计' else qua end as qua
, case when 'total' in ({select_brand}) then '合计' else brand end as 品牌
, case when 'total' in ({select_appid}) then '合计' else cast(appid as string) end as appid
, case when 'total' in ({select_package_name}) then '合计' else package_name end as package_name
, case when 'total' in ({select_app_name}) then '合计' else app_name end as app_name
, case when 'total' in ({fixed_total_params}) then '合计' else guid end as guid
, case when 'total' in ({fixed_total_params}) then '合计' when 'filter' in ({fixed_total_params}) then '排除恶意' else evil_level end as 恶意等级
, case when 'total' in ({fixed_total_params}) then '合计' else sim_download_id end as download_id
, case when 'total' in ({fixed_total_params}) then '合计' else game_coop_type end as 精品联运
, case when 'total' in ({fixed_total_params}) then '合计' else os_version end as os_version
, case when 'total' in ({fixed_total_params}) then '合计' else scene end as scene
, case when 'total' in ({fixed_total_params}) then '合计' else scene_name end as scene_name
, case when 'total' in ({fixed_total_params}) then '合计' else cast(ui_type as string) end as ui_type
, case when 'total' in ({fixed_total_params}) then '合计' else start_network end as 网络类型
, case when 'total' in ({fixed_total_params}) then '合计' else downloadResult end as 下载结果
, case when 'total' in ({fixed_total_params}) then '合计' else cast(down_error_code as string) end as 下载失败错误码
, case when 'total' in ({fixed_total_params}) then '合计' when AppSuccInstall=1 then 'succ' else 'not_succ' end as 安装结果
, case when 'total' in ({fixed_total_params}) then '合计' else down_type end as 下载类型

, count(1) 开始下载数
, sum(if(downloadResult='succ', 1, 0)) 下载成功数
, sum(AppSuccInstall) 安装成功数

, round(sum(if(downloadResult='succ', 1, 0)) * 100 / count(1), 2) 下载CVR
, round(sum(AppSuccInstall) * 100 / sum(if(downloadResult='succ', 1, 0)), 2) 安装CVR
, round(sum(AppSuccInstall) * 100 / count(1), 2) 下载安装CVR

from (
select
  ds
  , a.download_id download_id
  , a.guid guid
  , a.evil_level evil_level
  , a.sim_download_id sim_download_id
  , a.appid appid
  , a.package_name package_name
  , a.app_name app_name
  , a.scene scene
  , a.scene_name scene_name
  , a.ui_type ui_type
  , a.brand brand
  , a.os_version os_version
  , a.qua_big_version qua_big_version
  , a.qua qua
  , a.start_network start_network
  , a.game_coop_type game_coop_type

  , b.downloadResult downloadResult
  , b.AppSuccInstall AppSuccInstall
  , b2.down_error_code down_error_code

  , case
      when a.scene = '-1000' then '买量下载'
      when a.ui_type = 0 and (a.is_booking_pop_ui_task=1 or a.is_booking_task_bound = 1) then '预约静默下载'
      when a.ui_type = 0 and a.is_booking_pop_ui_task is null and a.is_booking_task_bound is null then '普通下载'
      else '其他下载'
      end
      as down_type

from
(
    select
        case when 'total' in ({fixed_total_params}) then '合计' else cast(ds as string) end as ds
        , concat(guid, download_id) download_id
        , max(guid) guid
        , nvl(max(evil_level),'0') evil_level
        , max(download_id) sim_download_id
        , max(appid) appid
        , max(package_name) package_name
        , max(appname) app_name
        , max(scene) scene
        , max(scene_name) scene_name
        , max(ui_type) ui_type
        , max(case when yyb_brand not in ('huawei','xiaomi','redmi','oneplus','vivo','realme','honor','sprd','samsung','oppo','5g') then '其他' else yyb_brand end) brand
        , max(split_part(android_version,'.',1)) os_version
        , max(substr(qua, 6, 3)) qua_big_version
        , max(qua) qua
        , max(access_network) start_network
        , max(game_coop_type) game_coop_type
        , max(case when instr(event_value,'auto_down_patch_uuid')>0 then 1 end) is_booking_pop_ui_task
        , max(case when instr(event_value,'is_set_local_copy')>0 or instr(event_value,'is_set_down_proxy')>0 then 1 end) is_booking_task_bound
    from
        beacon_olap.dwd_yyb_event_log_app_event_mid_di
    where
        ds between {start_date} and {end_date}
        and case when 'total' in ({select_qua_big_version}) then 1=1 else substr(qua, 6, 3) in ({select_qua_big_version}) end
        and case when 'total' in ({select_qua}) then 1=1 else qua in ({select_qua}) end
        and case when 'total' in ({select_brand}) then 1=1 when 'expand' in ({select_brand}) then 1=1 else yyb_brand in ({select_brand}) end
        and case when 'total' in ({select_appid}) then 1=1 else cast(appid as string) in ({select_appid}) end
        and case when 'total' in ({select_package_name}) then 1=1 else package_name in ({select_package_name}) end
        and case when 'total' in ({select_app_name}) then 1=1 else appname in ({select_app_name}) end
        and event_code in ('AppStartDownload')
        and instr(event_value,'booking_pre_pkg_type')<1 --去掉预约静默资源包下载
    group by
        ds, download_id
)a --开始下载，多维度

left join(
    select
        download_id
        , case  when event_code_list like '%AppSuccDownload%' then 'succ'
                when event_code_list like '%AppDelDownload%' then 'del'
                when event_code_list like '%AppFailDownload%' then 'fail'
                when event_code_list like '%AppPauseDownload%' then 'pause'
                else 'unfinish' end as downloadResult
        , case when event_code_list like '%AppSuccInstall%' then 1 else 0 end as AppSuccInstall
    from(
        select
            concat(guid, download_id) download_id
            , group_concat(distinct event_code) as event_code_list

        from
            beacon_olap.dwd_yyb_event_log_app_event_mid_di
        where
            ds between {start_date} and {end_date}
            and case when 'total' in ({select_qua_big_version}) then 1=1 else substr(qua, 6, 3) in ({select_qua_big_version}) end
            and case when 'total' in ({select_qua}) then 1=1 else qua in ({select_qua}) end
            and case when 'total' in ({select_brand}) then 1=1 when 'expand' in ({select_brand}) then 1=1 else yyb_brand in ({select_brand}) end
            and case when 'total' in ({select_appid}) then 1=1 else cast(appid as string) in ({select_appid}) end
            and case when 'total' in ({select_package_name}) then 1=1 else package_name in ({select_package_name}) end
            and case when 'total' in ({select_app_name}) then 1=1 else appname in ({select_app_name}) end

            and event_code in ('AppPauseDownload','AppSuccDownload','AppFailDownload','AppDelDownload','AppBeginInstall','AppSuccInstall','AppFailInstall')
        group by
            download_id
    )in_b
)b --下载结果/实验id
on a.download_id = b.download_id

left join(
    select
        concat(guid, download_id) download_id
        , max(cast(split_part(regexp_extract(event_value, 'taskerror=(.*)', 1), '&', 1) as bigint)) down_error_code

    from
        beacon_olap.dwd_yyb_event_log_app_event_mid_di
    where
        ds between {start_date} and {end_date}
        and case when 'total' in ({select_qua_big_version}) then 1=1 else substr(qua, 6, 3) in ({select_qua_big_version}) end
        and case when 'total' in ({select_qua}) then 1=1 else qua in ({select_qua}) end
        and case when 'total' in ({select_brand}) then 1=1 when 'expand' in ({select_brand}) then 1=1 else yyb_brand in ({select_brand}) end
        and case when 'total' in ({select_appid}) then 1=1 else cast(appid as string) in ({select_appid}) end
        and case when 'total' in ({select_package_name}) then 1=1 else package_name in ({select_package_name}) end
        and case when 'total' in ({select_app_name}) then 1=1 else appname in ({select_app_name}) end

        and event_code in ('AppFailDownload')
    group by
        download_id
)b2 --下载失败错误码
on b2.download_id = b.download_id and b.downloadResult='fail'

) source

group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20
order by 日期 desc,开始下载数 desc,下载成功数 desc,安装成功数 desc
        """

        # 构建请求体
        body = {
            "bizId": "yyb_bi",
            "plugin_model_conf": {
                "special_model_info": {
                    "sql": sql,
                    "sqlTpl": sql,
                    "dynamicSqls": [],
                    "sqlSyntaxCheck": True,
                    "variables": []
                },
                "common_model_info": {
                    "data_source_id": data_source_id,
                    "data_source_ids": [],
                    "result_desc": []
                }
            },
            "cacheable": False,
            "period": None,
            "periodDate": None,
            "queryType": None,
            "version": 1,
            "model_source": 1,
            "canBeaconTurbo": True,
            "variableInfo": None,
            "model_type": "sql",
            "is_edit": True
        }

        # 调用模型查询接口
        return self.post_model_query(body=body)


if __name__ == "__main__":
    APPLICATIONID = 2235
    SECRETKEY = "B40FF43E210D1B6363D3CA37DA2770B4"

    client = BeaconAPIClient(APPLICATIONID, SECRETKEY)

    # 查询指定图卡的数据
    params = {
        "async": 1,
        "cardId": 608812,  # 图卡id
        "bizId": "yyb_bi"  # 空间id
    }
    # response = client.get_card_result(**params)
    # print("get_card_result状态码:", response.status_code)
    # print("get_card_result响应内容:", response.text)

    # 查询指定仪表盘的数据
    biz_id = "yyb_bi"
    card_query = {
        "cacheStrategy": "FORCE",
        "cardId": "table_gg0nvee5",
        "limit": 200,
        "pageId": 239580,
        "paramsId": "7d118b20ee228ab45fc85dbd754fb643",
        "pushType": 0,
        "specialParam": {},
        "variables": [
            {
                "key": "version",
                "value": "'TMAF_858_P_9521','TMAF_858_P_9522','TMAF_858_P_9523','TMAF_857_P_9524','TMAF_858_P_9527','TMAF_858_P_9528','TMAF_858_P_9529','TMAF_857_P_9530'"
            },
            {
                "key": "imp_date.start",
                "value": 20250529
            },
            {
                "key": "imp_date.end",
                "value": 20250604
            }
        ]
    }
    # response2 = client.post_card_result(biz_id=biz_id, card_query=card_query)
    # print("post_card_result状态码:", response2.status_code)
    # print("post_card_result响应内容:", response2.text)

    sql ="""
select
    ds as 日期,
    case when 'total' in (#select_qua_big_version#) then '合计' else substr(qua, 6, 3) end as 大版本号,
    case when 'total' in (#select_qua#) then '合计' else qua end as 版本号,

    count(1) as 开始下载数,
    sum(case when downloadResult = 'succ' then 1 else 0 end) as 下载成功数,
    sum(AppSuccInstall) as 安装成功数,
    round(sum(AppSuccInstall) * 100.0 / count(1), 2) as 下载安装CVR

from (
    -- 开始下载事件
    select
        ds,
        guid,
        download_id,
        qua,
        substr(qua, 6, 3) as qua_big_version
    from beacon_olap.dwd_yyb_event_log_app_event_mid_di
    where ds between #tr1.start# and #tr1.end#
      and event_code = 'AppStartDownload'
      and case when 'total' in (#select_qua_big_version#) then 1=1 else substr(qua, 6, 3) in (#select_qua_big_version#) end
      and case when 'total' in (#select_qua#) then 1=1 else qua in (#select_qua#) end
) a

left join (
    -- 下载结果和安装成功标记
    select
        concat(guid, download_id) as download_id,
        case
            when event_code_list like '%AppSuccDownload%' then 'succ'
            when event_code_list like '%AppDelDownload%' then 'del'
            when event_code_list like '%AppFailDownload%' then 'fail'
            when event_code_list like '%AppPauseDownload%' then 'pause'
            else 'unfinish'
        end as downloadResult,
        max(case when event_code_list like '%AppSuccInstall%' then 1 else 0 end) as AppSuccInstall
    from (
        select
            guid,
            download_id,
            group_concat(distinct event_code) as event_code_list
        from beacon_olap.dwd_yyb_event_log_app_event_mid_di
        where ds between #tr1.start# and #tr1.end#
          and event_code in ('AppSuccDownload','AppDelDownload','AppFailDownload','AppPauseDownload','AppSuccInstall')
        group by guid, download_id
    ) b
    group by download_id, downloadResult
) b on concat(a.guid, a.download_id) = b.download_id

group by ds, 大版本号, 版本号
order by ds desc
    """
    
    # 调用模型查询接口示例
    body = {
        "bizId": "yyb_bi",
        "plugin_model_conf": {
            "special_model_info": {
                "sql": sql,
                "sqlTpl": sql,
                "dynamicSqls": [],
                "sqlSyntaxCheck": True,
                "variables": []
            },
            "common_model_info": {
                "data_source_id": 1235751,
                "data_source_ids": [],
                "result_desc": []
            }
        },
        "cacheable": False,
        "period": None,
        "periodDate": None,
        "queryType": None,
        "version": 1,
        "model_source": 1,
        "canBeaconTurbo": True,
        "variableInfo": None,
        "model_type": "sql",
        "is_edit": True
    }

    response = client.post_model_query(body=body)
    print("post_model_query状态码:", response.status_code)
    print("post_model_query响应内容:", response.text)

    print("\n" + "="*50)
    print("测试新增的下载安装CVR查询功能")
    print("="*50)

    # 测试新增的下载安装CVR查询功能
    cvr_response = client.query_download_install_cvr(
        start_date="20250529",
        end_date="20250604",
        qua_big_versions=["858", "857"],  # 指定QUA大版本
        brands=["huawei", "xiaomi"],      # 指定品牌
        package_names=None,               # 查询所有包名
        app_names=None,                   # 查询所有应用名
        appids=None                       # 查询所有应用ID
    )

    print("下载安装CVR查询状态码:", cvr_response.status_code)
    print("下载安装CVR查询响应内容:", cvr_response.text)
