import hashlib
import requests as req
import time
import random


if __name__ == '__main__':
    paasId = 'yyb_ai'  # 在太湖注册获得的应用id
    paasToken = '0PUOHJFWRP6NJY8ZCWEVKXY5N7CW5LSN'  # 在太湖注册获得的签名密钥
    server = "http://api-idc.sgw.woa.com"  # 智能网关在OA区的接入点域名
    # server = "http://api-idc.sgw.woa.com"   # 智能网关在IDC区的接入点域名
    # server = "http://api-idc.sgw.woa.com"   # 智能网关在DevCloud区的接入点域名
    # path = "/ebus/iwiki/prod/tencent/api/_version"  # 在太湖订阅接口成功后，获得的接口path
    path = "/ebus/iwiki/prod/tencent/api/vika/third/fields"  # 在太湖订阅接口成功后，获得的接口path
    params = {"doc_id":4015332476,"fieldId":"fldLoTGn9vHge"}  # 接口入参
    timestamp = str(int(time.time()))  # 生成时间戳，注意服务器的时间与标准时间差不能大于180秒
    nonce = str(random.randint(1000, 9999))  # 随机字符串，十分钟内不重复即可
    signature = hashlib.sha256()
    string = timestamp + paasToken + nonce + timestamp
    signature.update(string.encode())
    signature = signature.hexdigest().upper()  # 输出大写的结果
    header = {}
    #  设置鉴权参数，如果此参数设置错误，将触发“AGW.xxxxx”类型的错误，详见3.4章节
    header['x-rio-paasid'] = paasId
    header['x-rio-nonce'] = nonce
    header['x-rio-timestamp'] = timestamp
    header['x-rio-signature'] = signature
    req1 = req.Session()
    # 这里主要展示http head的构造，省略了http body的构造。
    response = req1.delete(url=server+path, params=params,headers=header)
    code = response.status_code
    print(server + path)
    print(code)
    print(response.headers)
    print(response.text)



# !/usr/bin/python
# -*- coding: UTF-8 -*-
import hashlib
import requests as req
import time
import random

if __name__ == '__main__':
    paasId = 'yyb_ai'  # 在太湖注册获得的应用id
    paasToken = '0PUOHJFWRP6NJY8ZCWEVKXY5N7CW5LSN'  # 在太湖注册获得的签名密钥
    server = "http://api-idc.sgw.woa.com"  # 智能网关在OA区的接入点域名
    path = "/ebus/iwiki/prod/tencent/api/vika/third/records"  # 在太湖订阅接口成功后，获得的接口path
    params = {"doc_id":4015363529,"record_ids":['recvgUitmTMPW', 'recvuMlxCQwA6', 'recFBQAUEMCW5']}  # 接口入参
    timestamp = str(int(time.time()))  # 生成时间戳，注意服务器的时间与标准时间差不能大于180秒
    nonce = str(random.randint(1000, 9999))  # 随机字符串，十分钟内不重复即可
    signature = hashlib.sha256()
    string = timestamp + paasToken + nonce + timestamp
    signature.update(string.encode())
    signature = signature.hexdigest().upper()  # 输出大写的结果
    header = {}
    #  设置鉴权参数，如果此参数设置错误，将触发“AGW.xxxxx”类型的错误，详见3.4章节
    header['x-rio-paasid'] = paasId
    header['x-rio-nonce'] = nonce
    header['x-rio-timestamp'] = timestamp
    header['x-rio-signature'] = signature
    req1 = req.Session()
    # 这里主要展示http head的构造，省略了http body的构造。
    response = req1.delete(url=server+path, params=params,headers=header)
    code = response.status_code
    print(server + path)
    print(code)
    print(response.headers)
    print(response.text)
