from pathlib import Path
import json
import os
import sys
import importlib.util


class LogRag:
    def __init__(self):
        self._codeExplain = {}

    def prepareLogRag(self):
        # 尝试多种可能的数据路径
        data_loaded = False
        
        # 方法1: 尝试从当前目录或父目录查找
        if not data_loaded:
            try:
                # 尝试在当前目录和上级目录中查找 rag/data
                current_dir = Path.cwd()
                for test_path in [current_dir, current_dir.parent]:
                    data_dir = test_path / 'rag' / 'data'
                    if data_dir.exists() and data_dir.is_dir():
                        self._load_data_from_dir(data_dir)
                        data_loaded = True
                        print(f"成功从 {data_dir} 加载数据")
                        break
            except Exception as e:
                print(f"从本地目录加载数据失败: {e}")
        
        # 方法2: 尝试查找安装包中的数据
        if not data_loaded:
            try:
                # 在所有可能的路径中查找
                for path in sys.path:
                    for pkg_name in ['AiLogs']:
                        data_dir = Path(path) / pkg_name / 'rag' / 'data'
                        if data_dir.exists() and data_dir.is_dir():
                            self._load_data_from_dir(data_dir)
                            data_loaded = True
                            print(f"成功从 {data_dir} 加载数据")
                            break
                    if data_loaded:
                        break
            except Exception as e:
                print(f"在 sys.path 中搜索数据失败: {e}")
        
        if not data_loaded:
            print("警告: 未能从任何位置加载 RAG 数据，请确保数据文件存在并正确安装")
            print("尝试的路径包括: sys.path, 当前目录, 上级目录, 和包资源")
    
    def _load_data_from_dir(self, dir_path):
        """从指定目录加载数据文件"""
        count = 0
        for df in Path(dir_path).iterdir():
            if df.is_file():
                with df.open() as f:
                    for line in f:
                        try:
                            r = json.loads(line)
                            self._codeExplain[r['query']] = r['value']
                            count += 1
                        except json.JSONDecodeError:
                            print(f"警告: 跳过无效的 JSON 行: {line}")
        print(f"已加载 {count} 条数据记录")

    def queryExplainByCode(self, code):
        if self._codeExplain.get(code) is not None:
            return self._codeExplain[code]
        return None


# 创建一个全局可用的索引
log_rag = LogRag()
