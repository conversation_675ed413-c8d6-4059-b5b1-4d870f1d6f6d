import os
import sys
import json
from log_tool.rag.log_rag import log_rag
from common.logs.logger import app_logger
from common.client.hunyuan_client import HunyuanClient
from log_tool.prompt.user_action_prompt import USER_ACTION_PROMPT
from log_tool.agent.base_agent import BaseAgent
from common.tools.log_filter import LogFilter
from common.error.detailed_value_error import raise_value_error, ErrorCode
from common.client.iwiki_client import IWikiClient
from common.tools.text_parse_utils import TextParseUtils, LogSceneConfig
from common.tools.file_utils import append_to_file
from log_tool.agent.user_intent_parser_agent import UserIntentParser

# 过滤用户的行为日志
# 单独执行该文件：python3 -m agent.filter_user_action

class FilterUserAction:
    def __init__(self, query, logs_path, **kwargs):
        self._query = query
        self._logs_path = logs_path

        # 这些是固定的内部属性
        self._userActionTags = [
            'DownloadProxy',
            'InstallStManager',
            'event add:AppBeginInstall',
            'loadKuiklyRenderView onComplete pageInfo',
            'RuntimeCrash',
            'BaseActivity'
        ]
        self._user_action_index_keys = {'scene'}
        self._error_action = ''

        # 通过 kwargs 获取可选参数，避免可变默认参数陷阱
        self._log_tags = kwargs.get('log_tags') or []
        self._prompt = kwargs.get('prompt', )
        self._bug_time = kwargs.get('bug_time') or ''
        self._index_keys = kwargs.get('index_keys') or set()
        self._save_logs = kwargs.get('save_logs') or []
        self._delete_logs = kwargs.get('delete_logs') or []
        self._split_info = kwargs.get('split_info') or []
        self._dedup_targets = kwargs.get('dedup_targets') or []
        self._extract_fields = kwargs.get('extract_fields') or {}
        self._is_fuzzy_match_tag = kwargs.get('is_fuzzy_match_tag', False)
        self._is_analyze_daemon = kwargs.get('is_analyze_daemon', False)
        self._tag_ignore_patterns = kwargs.get('tag_ignore_patterns') or {}
        self._placeholder = kwargs.get('placeholder', '')
        self._ticket_id = kwargs.get('ticket_id') or ''
        self._iwiki_client = IWikiClient()


    def parse_user_action(self):
        # 获取用户意图
        user_query_scene, url = UserIntentParser.parse_user_intent(query=self._query,ticket_id=self._ticket_id)

        # 如果识别到场景，进行Prompt场景iwiki解析
        parse_iwiki_content = None
        if user_query_scene and url:
            iwiki_data = self._iwiki_client.get_doc_body(url)
            if iwiki_data:
                parse_iwiki_content = TextParseUtils.parse(
                    text=iwiki_data,
                    config_cls=LogSceneConfig,
                    section_prefix=">>> "
                )
                if not parse_iwiki_content:
                    raise_value_error(ErrorCode.IWIKI_PARSE_ERROR, message=f"iwiki内容解析失败\niwiki链接：{url}\n")
                
                app_logger.info(f'>>> ticket_id = {self._ticket_id}\n')
                if self._ticket_id:
                    append_to_file(self._ticket_id, f'>>> 解析出的 日志分析 配置信息：\n{parse_iwiki_content.format_all_fields()}\n')
        else:
            # raise ValueError(f"未找到匹配的场景或对应的URL\n场景：{user_query_scene}，iwiki链接：{url}\n")
            raise_value_error(ErrorCode.USER_ACTION_NOT_FOUND, message=f"未找到匹配的场景或对应的URL\n场景：{user_query_scene}，iwiki链接：{url}\n")

        # 如果开启分析用户行为，进行用户行为分析
        user_action = ''
        if parse_iwiki_content.is_analyze_user_action:
            app_logger.info('======= 开始 分析用户操作行为链 ...  =========')

            save_logs = [
                ('DownloadProxy', ''),
                ('BaseActivity', ''),
                ('Kuikly-KRCommonActivity', 'loadKuiklyRenderView onComplete pageInfo'),
                ('InstallRetryMgr', 'event add:AppBeginInstall'),
                ('RuntimeCrash', '')
            ]

            delete_logs = [
                ('DownloadProxy', 'fileType=PLUGIN'),
                ('BaseActivity', '页面曝光'),
                ('BaseActivity', 'passphrase'),
                ('BaseActivity', 'showFloatingBall'),
                ('BaseActivity', 'onUserLeaveHint'),
                ('BaseActivity', 'disMissKeyGuard')
            ]

            log_filter = LogFilter(
                logs_path=self._logs_path,
                log_tags=self._userActionTags,
                bug_time=self._bug_time,
                index_keys=self._user_action_index_keys,
                save_logs=save_logs,
                delete_logs=delete_logs
            )

            filtered_logs, _, rag, _ = log_filter.get_filtered_log()

            prompt = USER_ACTION_PROMPT.format(rag=rag, log_content="".join(filtered_logs))
            items = self._request_model(prompt)
            for result in items:
                if result['type'] == 'all_answer':
                    if result['data']:    
                        user_action = result['data']
                    else:
                        yield {"data": "混元模型过载，请重试", "type": 'reasoning_content'}
                elif result['type'] in ('answer', 'thinking'):
                    yield {"data": result['data'], "type": 'reasoning_content'}

            app_logger.info('======= 结束 分析用户操作行为链  =========')

        # ai分析
        base_agent = BaseAgent(
            userAction=user_action,
            query=self._query,
            logs_path=self._logs_path,
            log_tags=parse_iwiki_content.filter_tag,
            prompt=parse_iwiki_content.prompt,
            bug_time=self._bug_time,
            index_keys=parse_iwiki_content.index_keys,
            save_logs=parse_iwiki_content.save_logs,
            delete_logs=parse_iwiki_content.delete_logs,
            split_info=parse_iwiki_content.split_logs,
            dedup_targets=parse_iwiki_content.dedup_logs,
            extract_fields=parse_iwiki_content.extract_fields,
            is_fuzzy_match_tag=parse_iwiki_content.is_fuzzy_match_tag,
            is_analyze_daemon=parse_iwiki_content.is_analyze_daemon,
            tag_ignore_patterns=parse_iwiki_content.tag_ignore_patterns,
            placeholder=parse_iwiki_content.placeholder,
            ticket_id=self._ticket_id,
            is_analyze_all_logs=parse_iwiki_content.is_analyze_all_logs
        )
        yield from self.set_stream_output(base_agent.parse_log())
        
        
    def set_stream_output(self, items):
        if items is None:
            app_logger.info(f"流式结果为空")
            raise_value_error(ErrorCode.MODEL_STREAM_RESULT_EMPTY, message="流式结果为空")
            # raise ValueError(f"流式结果为空")
        # 中间过程：type=reasoning_content
        # 回答内容（流式片段）: type=content_answer
        # 最终答案(全部)：type=result_answer
        for result in items:
            if result['type'] == 'thinking':
                yield {"data": result['data'], "type": 'reasoning_content'}
            elif result['type'] == 'answer':
                yield {"data": result['data'], "type": 'content_answer'}
            elif result['type'] == 'all_answer':
                if result['data']:
                    yield {"data": result['data'], "type": 'result_answer'}
                else:
                    yield {"data": "混元模型过载，请重试", "type": 'reasoning_content'}
            else:
                yield {"data": result['data'], "type": 'unknown'}

    
    
    # 用户操作路径分析
    def _request_model(self, prompt):
        app_logger.info(prompt)
        ss_url = 'http://stream-server-online-openapi.turbotke.production.polaris:8080/openapi/chat/completions'
        model = "DeepSeek-R1-Online"
        wsid = "11417"
        token = "00ac8819-7488-4487-bfbd-17f4d760aed8"
        is_stream = True
        enable_enhancement = False

        hunyuan_client = HunyuanClient(ss_url, wsid, model, token, is_stream, enable_enhancement)
        # 使用 DeepSeek 专用方法
        yield from self._stream_results(hunyuan_client.request_deepseek(prompt))
    
    def _stream_results(self, results):
        for result in results:
            yield {"data": result['data'], "type": result['type']}
    
    # 思考过程 type转换
    def agent_stream_results(self, items):
        if items is None:
            app_logger.info(f"流式结果为空")
            raise_value_error(ErrorCode.MODEL_STREAM_RESULT_EMPTY, message="流式结果为空")
            # raise ValueError(f"流式结果为空")
        for result in items:
            if result['type'] == 'all_answer':
                self._error_action = self._error_action + result['data'] +'\n'
            elif result['type'] == 'answer' or result['type'] == 'thinking':
                yield {"data": result['data'], "type": 'reasoning_content'}


if __name__ == '__main__':
    logPath = ''
    startTime = ''
    endTime = ''
    query = '活动页'
    filterUserAction = FilterUserAction(query=query, logs_path=logPath, ticket_id = 'xx')
    filterUserAction.parse_user_action()
