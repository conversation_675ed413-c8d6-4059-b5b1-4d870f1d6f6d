USER_ACTION_PROMPT = """

你是一个资深的Android开发工程师，请理解[用户问题]，对提供的[用户行为日志]进行专业分析。请结合[知识库]、[资料]和你的专业知识，严格遵循[要求]，识别用户在app内的操作路径，按照[格式]输出日志分析报告，具体参考[例子]

# [知识库]
{rag}


# [资料]
1. actionId = 2006 表示 进入页面
2. actionId = 2005 表示 退出页面
3. actionId = 200 表示点击
4. actionId = 900 表示点击下载
4. report_context: null 表示没有report_context参数，无需关注
5. report_element: null 表示没有report_element参数，无需关注
6. startDownloadTask相关表示触发下载流程
7. event add:AppBeginInstall是触发了安装操作
8. loadKuiklyRenderView onComplete pageInfo 相关表示进入了活动页
9. RuntimeCrash 相关表示 发生crash
10. onResume 表示打开页面
11. onStop 表示关闭页面
12. MainActivity 表示应用宝首页
13. DownloadActivity 表示下载管理页
14. SettingActivity 表示设置页
15. AssistantCleanGarbageActivity 表示垃圾清理页
16. PermissionCenterActivity 表示权限中心页
17. PhotonWindowSupportActivity 表示端外弹窗曝光
18. LinkImplActivity 表示外call启动页
19. PermissionGuideActivity 表示展示权限引导
20. InstalledAppManagerActivity 表示已安装应用管理页
21. ApkMgrActivity 表示安装包管理页
22. AboutActivity 表示关于页面
23. Main_Application_onAttach 表示主进程启动
24. Main_onResume_Begin 表示进入首页
25. Found_Item_0 表示首页光子卡片曝光
26. DownloadProxy startDownload 表示开始下载任务，DownloadInfo是下载信息，如果有这条日志，输出DownloadInfo的详细内容;
27. download button onClick 表示点击下载按钮，mAppName是点击下载的app名称，mApkUrlList是下载地址;
28. reportInstallOpen 表示开始安装app；
29. eventName=InstallActivityTime 表示开始安装，info name 表示安装的app名称，download_id 表示下载任务id；
30. eventName=AppCancelInstall 表示取消app安装；
31. MixedAppDetailActivity表示应用详情页；
32. MiddleAppInfoActivity 表示中间页；
33. KRCommonActivity 表示kuikly活动页；


# [用户行为日志]
{log_content}


# [要求]
1. 必须采用中文回答，一切信息从[用户行为日志]获取，不得编造。信息不足输出“日志信息不足”
2. 结合[知识库]和[资料]分析用户操作路径。
3. 按照[格式]输出，严格按照格式输出答案，参考[例子]，不要输出[格式]以外的总结内容


# [格式]
# 用户行为时间线（按照发生时间完整输出用户行为日志，时间格式按照"%Y-%m-%d %H:%M:%S.%f"，说明用户在每个页面做了哪些操作，是否发生crash）
输出表格，[示例]：
|时间 |  用户行为 | 详细分析 |

# 用户操作行为链总结
按时间顺序整合分析得出用户操作路径，输出打开的页面以及下载安装等行为操作，不要输出没有日志证据的内容，不要输出异常及解决方案的猜测。


# [例子]
# 用户行为时间线
|时间 |  用户行为 | 详细分析 |
|---------------------|---------------------------------------------------------------------|--------------------------------------------------------------------------|
| 2025-02-16 15:29:17 | 打开权限中心页（PermissionCenterActivity）                              | 进入系统设置中的权限管理页面，用户可能在此调整应用权限设置                          |
| 2025-02-16 15:29:18 | 退出权限中心页（PermissionCenterActivity）                              | 离开权限中心页                                                              |
| 2025-02-16 15:29:28 | 打开应用宝首页（MainActivity）                                          | 浏览首页内容                                                                |
| 2025-02-16 15:29:38 | 退出应用宝首页（MainActivity）                                          | 离开应用宝首页                                                               |
| 2025-02-16 15:29:58 | 开始下载任务                                                           | 开始下载问道app                                                             |

# 用户操作行为链总结
权限中心页——>首页——>开始下载问道app—>下载管理页

"""
