GRAY_DATA_NEW_USERS ="""
# 灰度新用户 数据
## crash率 & ANR率
<table border="1" cellspacing="0" cellpadding="5" style="border-collapse: collapse;">
  <thead>
    <tr>
      <th>灰度新用户</th>
      <th>QUA</th>
      <th>设备crash率<br>(<=0.18%)</th>
      <th>设备平均崩溃率<br>(<=2%)</th>
      <th>前台设备崩溃率<br>(<=0.06%)</th>
      <th>ANR率<br>(<=0.05%)</th>
      <th>灰度日期</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td rowspan="4">实验组1</td>
      <td rowspan="4">{qua_1}</td>
      <td>{device_crash_1_day1}</td>
      <td>{avg_device_crash_1_day1}</td>
      <td>{fg_device_crash_1_day1}</td>
      <td>{anr_rate_1_day1}</td>
      <td>{date_1_day1}</td>
    </tr>
    <tr>
      <td>{device_crash_1_day2}</td>
      <td>{avg_device_crash_1_day2}</td>
      <td>{fg_device_crash_1_day2}</td>
      <td>{anr_rate_1_day2}</td>
      <td>{date_1_day2}</td>
    </tr>
    <tr>
      <td>{device_crash_1_day3}</td>
      <td>{avg_device_crash_1_day3}</td>
      <td>{fg_device_crash_1_day3}</td>
      <td>{anr_rate_1_day3}</td>
      <td>{date_1_day3}</td>
    </tr>
    <tr>
       <td>{device_crash_1_day4}</td>
       <td>{avg_device_crash_1_day4}</td>
       <td>{fg_device_crash_1_day4}</td>
       <td>{anr_rate_1_day4}</td>
       <td>{date_1_day4}</td>
     </tr>

    <tr>
      <td rowspan="4">实验组2</td>
      <td rowspan="4">{qua_2}</td>
      <td>{device_crash_2_day1}</td>
      <td>{avg_device_crash_2_day1}</td>
      <td>{fg_device_crash_2_day1}</td>
      <td>{anr_rate_2_day1}</td>
      <td>{date_2_day1}</td>
    </tr>
    <tr>
      <td>{device_crash_2_day2}</td>
      <td>{avg_device_crash_2_day2}</td>
      <td>{fg_device_crash_2_day2}</td>
      <td>{anr_rate_2_day2}</td>
      <td>{date_2_day2}</td>
    </tr>
    <tr>
      <td>{device_crash_2_day3}</td>
      <td>{avg_device_crash_2_day3}</td>
      <td>{fg_device_crash_2_day3}</td>
      <td>{anr_rate_2_day3}</td>
      <td>{date_2_day3}</td>
    </tr>
    <tr>
      <td>{device_crash_2_day4}</td>
      <td>{avg_device_crash_2_day4}</td>
      <td>{fg_device_crash_2_day4}</td>
      <td>{anr_rate_2_day4}</td>
      <td>{date_2_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">实验组3</td>
      <td rowspan="4">{qua_3}</td>
      <td>{device_crash_3_day1}</td>
      <td>{avg_device_crash_3_day1}</td>
      <td>{fg_device_crash_3_day1}</td>
      <td>{anr_rate_3_day1}</td>
      <td>{date_3_day1}</td>
    </tr>
    <tr>
      <td>{device_crash_3_day2}</td>
      <td>{avg_device_crash_3_day2}</td>
      <td>{fg_device_crash_3_day2}</td>
      <td>{anr_rate_3_day2}</td>
      <td>{date_3_day2}</td>
    </tr>
    <tr>
      <td>{device_crash_3_day3}</td>
      <td>{avg_device_crash_3_day3}</td>
      <td>{fg_device_crash_3_day3}</td>
      <td>{anr_rate_3_day3}</td>
      <td>{date_3_day3}</td>
    </tr>
    <tr>
      <td>{device_crash_3_day4}</td>
      <td>{avg_device_crash_3_day4}</td>
      <td>{fg_device_crash_3_day4}</td>
      <td>{anr_rate_3_day4}</td>
      <td>{date_3_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">对照组</td>
      <td rowspan="4">{qua_control}</td>
      <td>{device_crash_control_day1}</td>
      <td>{avg_device_crash_control_day1}</td>
      <td>{fg_device_crash_control_day1}</td>
      <td>{anr_rate_control_day1}</td>
      <td>{date_control_day1}</td>
    </tr>
    <tr>
      <td>{device_crash_control_day2}</td>
      <td>{avg_device_crash_control_day2}</td>
      <td>{fg_device_crash_control_day2}</td>
      <td>{anr_rate_control_day2}</td>
      <td>{date_control_day2}</td>
    </tr>
    <tr>
      <td>{device_crash_control_day3}</td>
      <td>{avg_device_crash_control_day3}</td>
      <td>{fg_device_crash_control_day3}</td>
      <td>{anr_rate_control_day3}</td>
      <td>{date_control_day3}</td>
    </tr>
    <tr>
      <td>{device_crash_control_day4}</td>
      <td>{avg_device_crash_control_day4}</td>
      <td>{fg_device_crash_control_day4}</td>
      <td>{anr_rate_control_day4}</td>
      <td>{date_control_day4}</td>
    </tr>
  </tbody>
</table>


## 启动速度


| 灰度新用户 | QUA |常规热启动<br>(gap<50ms) | 常规冷启动<br>(gap<100ms) | 常规外call热启动<br>(gap<50ms) | 常规外call冷启动<br>(gap<50ms) |
|--------|--------------|---------|------|------|-------|
|实验组1|{qua_1}|{regular_hot_1}|{regular_cold_1}|{regular_outcall_hot_1}|{regular_outcall_cold_1}|
|实验组2|{qua_2}|{regular_hot_2}|{regular_cold_2}|{regular_outcall_hot_2}|{regular_outcall_cold_2}|
|实验组3|{qua_3}|{regular_hot_3}|{regular_cold_3}|{regular_outcall_hot_3}|{regular_outcall_cold_3}|
|对照组|{qua_control}|{regular_hot_control}|{regular_cold_control}|{regular_outcall_hot_control}|{regular_outcall_cold_control}|

## 下载相关
<table border="1" cellspacing="0" cellpadding="5" style="border-collapse: collapse;">
  <thead>
    <tr>
      <th>灰度新用户</th>
      <th>QUA</th>
      <th>外call开始下载率<br>(gap<0.7%)</th>
      <th>外call成功下载率<br>(gap<0.7%)</th>
      <th>下载安装CVR(%)<br>(大盘下载安装cvr)<br>(gap<1%)</th>
      <th>灰度日期</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td rowspan="4">实验组1</td>
      <td rowspan="4">{qua_1}</td>
      <td>{outcall_start_download_1_day1}</td>
      <td>{outcall_success_download_1_day1}</td>
      <td>{download_install_cvr_1_day1}</td>
      <td>{date_1_day1}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_1_day2}</td>
      <td>{outcall_success_download_1_day2}</td>
      <td>{download_install_cvr_1_day2}</td>
      <td>{date_1_day2}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_1_day3}</td>
      <td>{outcall_success_download_1_day3}</td>
      <td>{download_install_cvr_1_day3}</td>
      <td>{date_1_day3}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_1_day4}</td>
      <td>{outcall_success_download_1_day4}</td>
      <td>{download_install_cvr_1_day4}</td>
      <td>{date_1_day4}</td>
     </tr>

    <tr>
      <td rowspan="4">实验组2</td>
      <td rowspan="4">{qua_2}</td>
      <td>{outcall_start_download_2_day1}</td>
      <td>{outcall_success_download_2_day1}</td>
      <td>{download_install_cvr_2_day1}</td>
      <td>{date_2_day1}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_2_day2}</td>
      <td>{outcall_success_download_2_day2}</td>
      <td>{download_install_cvr_2_day2}</td>
      <td>{date_2_day2}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_2_day3}</td>
      <td>{outcall_success_download_2_day3}</td>
      <td>{download_install_cvr_2_day3}</td>
      <td>{date_2_day3}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_2_day4}</td>
      <td>{outcall_success_download_2_day4}</td>
      <td>{download_install_cvr_2_day4}</td>
      <td>{date_2_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">实验组3</td>
      <td rowspan="4">{qua_3}</td>
      <td>{outcall_start_download_3_day1}</td>
      <td>{outcall_success_download_3_day1}</td>
      <td>{download_install_cvr_3_day1}</td>
      <td>{date_3_day1}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_3_day2}</td>
      <td>{outcall_success_download_3_day2}</td>
      <td>{download_install_cvr_3_day2}</td>
      <td>{date_3_day2}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_3_day3}</td>
      <td>{outcall_success_download_3_day3}</td>
      <td>{download_install_cvr_3_day3}</td>
      <td>{date_3_day3}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_3_day4}</td>
      <td>{outcall_success_download_3_day4}</td>
      <td>{download_install_cvr_3_day4}</td>
      <td>{date_3_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">对照组</td>
      <td rowspan="4">{qua_control}</td>
      <td>{outcall_start_download_control_day1}</td>
      <td>{outcall_success_download_control_day1}</td>
      <td>{download_install_cvr_control_day1}</td>
      <td>{date_control_day1}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_control_day2}</td>
      <td>{outcall_success_download_control_day2}</td>
      <td>{download_install_cvr_control_day2}</td>
      <td>{date_control_day2}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_control_day3}</td>
      <td>{outcall_success_download_control_day3}</td>
      <td>{download_install_cvr_control_day3}</td>
      <td>{date_control_day3}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_control_day4}</td>
      <td>{outcall_success_download_control_day4}</td>
      <td>{download_install_cvr_control_day4}</td>
      <td>{date_control_day4}</td>
    </tr>
  </tbody>
</table>

## 广告相关
<table border="1" cellspacing="0" cellpadding="5" style="border-collapse: collapse;">
  <thead>
    <tr>
      <th>灰度新用户</th>
      <th>QUA</th>
      <th>广告-曝光</th>
      <th>广告-点击</th>
      <th>广告-下载</th>
      <th>广告-安装</th>
      <th>点击 / 曝光<br>(gap<=0.001)</th>
      <th>下载 / 点击<br>(gap<=0.1)</th>
      <th>安装 / 下载<br>(gap<=0.1)</th>
      <th>灰度日期</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td rowspan="4">实验组1</td>
      <td rowspan="4">{qua_1}</td>
      <td>{ad_exposure_1_day1}</td>
      <td>{ad_click_1_day1}</td>
      <td>{ad_download_1_day1}</td>
      <td>{ad_install_1_day1}</td>
      <td>{click_exposure_ratio_1_day1}</td>
      <td>{download_click_ratio_1_day1}</td>
      <td>{install_download_ratio_1_day1}</td>
      <td>{date_1_day1}</td>
    </tr>
    <tr>
      <td>{ad_exposure_1_day2}</td>
      <td>{ad_click_1_day2}</td>
      <td>{ad_download_1_day2}</td>
      <td>{ad_install_1_day2}</td>
      <td>{click_exposure_ratio_1_day2}</td>
      <td>{download_click_ratio_1_day2}</td>
      <td>{install_download_ratio_1_day2}</td>
      <td>{date_1_day2}</td>
    </tr>
    <tr>
      <td>{ad_exposure_1_day3}</td>
      <td>{ad_click_1_day3}</td>
      <td>{ad_download_1_day3}</td>
      <td>{ad_install_1_day3}</td>
      <td>{click_exposure_ratio_1_day3}</td>
      <td>{download_click_ratio_1_day3}</td>
      <td>{install_download_ratio_1_day3}</td>
      <td>{date_1_day3}</td>
    </tr>
    <tr>
      <td>{ad_exposure_1_day4}</td>
      <td>{ad_click_1_day4}</td>
      <td>{ad_download_1_day4}</td>
      <td>{ad_install_1_day4}</td>
      <td>{click_exposure_ratio_1_day4}</td>
      <td>{download_click_ratio_1_day4}</td>
      <td>{install_download_ratio_1_day4}</td>
      <td>{date_1_day4}</td>
     </tr>

    <tr>
      <td rowspan="4">实验组2</td>
      <td rowspan="4">{qua_2}</td>
      <td>{ad_exposure_2_day1}</td>
      <td>{ad_click_2_day1}</td>
      <td>{ad_download_2_day1}</td>
      <td>{ad_install_2_day1}</td>
      <td>{click_exposure_ratio_2_day1}</td>
      <td>{download_click_ratio_2_day1}</td>
      <td>{install_download_ratio_2_day1}</td>
      <td>{date_2_day1}</td>
    </tr>
    <tr>
      <td>{ad_exposure_2_day2}</td>
      <td>{ad_click_2_day2}</td>
      <td>{ad_download_2_day2}</td>
      <td>{ad_install_2_day2}</td>
      <td>{click_exposure_ratio_2_day2}</td>
      <td>{download_click_ratio_2_day2}</td>
      <td>{install_download_ratio_2_day2}</td>
      <td>{date_2_day2}</td>
    </tr>
    <tr>
      <td>{ad_exposure_2_day3}</td>
      <td>{ad_click_2_day3}</td>
      <td>{ad_download_2_day3}</td>
      <td>{ad_install_2_day3}</td>
      <td>{click_exposure_ratio_2_day3}</td>
      <td>{download_click_ratio_2_day3}</td>
      <td>{install_download_ratio_2_day3}</td>
      <td>{date_2_day3}</td>
    </tr>
    <tr>
      <td>{ad_exposure_2_day4}</td>
      <td>{ad_click_2_day4}</td>
      <td>{ad_download_2_day4}</td>
      <td>{ad_install_2_day4}</td>
      <td>{click_exposure_ratio_2_day4}</td>
      <td>{download_click_ratio_2_day4}</td>
      <td>{install_download_ratio_2_day4}</td>
      <td>{date_2_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">实验组3</td>
      <td rowspan="4">{qua_3}</td>
      <td>{ad_exposure_3_day1}</td>
      <td>{ad_click_3_day1}</td>
      <td>{ad_download_3_day1}</td>
      <td>{ad_install_3_day1}</td>
      <td>{click_exposure_ratio_3_day1}</td>
      <td>{download_click_ratio_3_day1}</td>
      <td>{install_download_ratio_3_day1}</td>
      <td>{date_3_day1}</td>
    </tr>
    <tr>
      <td>{ad_exposure_3_day2}</td>
      <td>{ad_click_3_day2}</td>
      <td>{ad_download_3_day2}</td>
      <td>{ad_install_3_day2}</td>
      <td>{click_exposure_ratio_3_day2}</td>
      <td>{download_click_ratio_3_day2}</td>
      <td>{install_download_ratio_3_day2}</td>
      <td>{date_3_day2}</td>
    </tr>
    <tr>
      <td>{ad_exposure_3_day3}</td>
      <td>{ad_click_3_day3}</td>
      <td>{ad_download_3_day3}</td>
      <td>{ad_install_3_day3}</td>
      <td>{click_exposure_ratio_3_day3}</td>
      <td>{download_click_ratio_3_day3}</td>
      <td>{install_download_ratio_3_day3}</td>
      <td>{date_3_day3}</td>
    </tr>
    <tr>
      <td>{ad_exposure_3_day4}</td>
      <td>{ad_click_3_day4}</td>
      <td>{ad_download_3_day4}</td>
      <td>{ad_install_3_day4}</td>
      <td>{click_exposure_ratio_3_day4}</td>
      <td>{download_click_ratio_3_day4}</td>
      <td>{install_download_ratio_3_day4}</td>
      <td>{date_3_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">对照组</td>
      <td rowspan="4">{qua_control}</td>
      <td>{ad_exposure_control_day1}</td>
      <td>{ad_click_control_day1}</td>
      <td>{ad_download_control_day1}</td>
      <td>{ad_install_control_day1}</td>
      <td>{click_exposure_ratio_control_day1}</td>
      <td>{download_click_ratio_control_day1}</td>
      <td>{install_download_ratio_control_day1}</td>
      <td>{date_control_day1}</td>
    </tr>
    <tr>
      <td>{ad_exposure_control_day2}</td>
      <td>{ad_click_control_day2}</td>
      <td>{ad_download_control_day2}</td>
      <td>{ad_install_control_day2}</td>
      <td>{click_exposure_ratio_control_day2}</td>
      <td>{download_click_ratio_control_day2}</td>
      <td>{install_download_ratio_control_day2}</td>
      <td>{date_control_day2}</td>
    </tr>
    <tr>
      <td>{ad_exposure_control_day3}</td>
      <td>{ad_click_control_day3}</td>
      <td>{ad_download_control_day3}</td>
      <td>{ad_install_control_day3}</td>
      <td>{click_exposure_ratio_control_day3}</td>
      <td>{download_click_ratio_control_day3}</td>
      <td>{install_download_ratio_control_day3}</td>
      <td>{date_control_day3}</td>
    </tr>
    <tr>
      <td>{ad_exposure_control_day4}</td>
      <td>{ad_click_control_day4}</td>
      <td>{ad_download_control_day4}</td>
      <td>{ad_install_control_day4}</td>
      <td>{click_exposure_ratio_control_day4}</td>
      <td>{download_click_ratio_control_day4}</td>
      <td>{install_download_ratio_control_day4}</td>
      <td>{date_control_day4}</td>
    </tr>
  </tbody>
</table>

## 联网用户数 & 弹窗 & 云游
<table border="1" cellspacing="0" cellpadding="5" style="border-collapse: collapse;">
  <thead>
    <tr>
      <th>灰度新用户</th>
      <th>QUA</th>
      <th>联网用户数</th>
      <th>弹窗成功率(%)<br>(gap<1%)</th>
      <th>云游插件拉起成功率<br>(>97%)</th>
      <th>灰度日期</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td rowspan="4">实验组1</td>
      <td rowspan="4">{qua_1}</td>
      <td>{net_user_count_1_day1}</td>
      <td>{popup_success_rate_1_day1}</td>
      <td>{cloud_game_plugin_launch_success_rate_1_day1}</td>
      <td>{date_1_day1}</td>
    </tr>
    <tr>
      <td>{net_user_count_1_day2}</td>
      <td>{popup_success_rate_1_day2}</td>
      <td>{cloud_game_plugin_launch_success_rate_1_day2}</td>
      <td>{date_1_day2}</td>
    </tr>
    <tr>
      <td>{net_user_count_1_day3}</td>
      <td>{popup_success_rate_1_day3}</td>
      <td>{cloud_game_plugin_launch_success_rate_1_day3}</td>
      <td>{date_1_day3}</td>
    </tr>
    <tr>
      <td>{net_user_count_1_day4}</td>
      <td>{popup_success_rate_1_day4}</td>
      <td>{cloud_game_plugin_launch_success_rate_1_day4}</td>
      <td>{date_1_day4}</td>
     </tr>

    <tr>
      <td rowspan="4">实验组2</td>
      <td rowspan="4">{qua_2}</td>
      <td>{net_user_count_2_day1}</td>
      <td>{popup_success_rate_2_day1}</td>
      <td>{cloud_game_plugin_launch_success_rate_2_day1}</td>
      <td>{date_2_day1}</td>
    </tr>
    <tr>
      <td>{net_user_count_2_day2}</td>
      <td>{popup_success_rate_2_day2}</td>
      <td>{cloud_game_plugin_launch_success_rate_2_day2}</td>
      <td>{date_2_day2}</td>
    </tr>
    <tr>
      <td>{net_user_count_2_day3}</td>
      <td>{popup_success_rate_2_day3}</td>
      <td>{cloud_game_plugin_launch_success_rate_2_day3}</td>
      <td>{date_2_day3}</td>
    </tr>
    <tr>
      <td>{net_user_count_2_day4}</td>
      <td>{popup_success_rate_2_day4}</td>
      <td>{cloud_game_plugin_launch_success_rate_2_day4}</td>
      <td>{date_2_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">实验组3</td>
      <td rowspan="4">{qua_3}</td>
      <td>{net_user_count_3_day1}</td>
      <td>{popup_success_rate_3_day1}</td>
      <td>{cloud_game_plugin_launch_success_rate_3_day1}</td>
      <td>{date_3_day1}</td>
    </tr>
    <tr>
      <td>{net_user_count_3_day2}</td>
      <td>{popup_success_rate_3_day2}</td>
      <td>{cloud_game_plugin_launch_success_rate_3_day2}</td>
      <td>{date_3_day2}</td>
    </tr>
    <tr>
      <td>{net_user_count_3_day3}</td>
      <td>{popup_success_rate_3_day3}</td>
      <td>{cloud_game_plugin_launch_success_rate_3_day3}</td>
      <td>{date_3_day3}</td>
    </tr>
    <tr>
      <td>{net_user_count_3_day4}</td>
      <td>{popup_success_rate_3_day4}</td>
      <td>{cloud_game_plugin_launch_success_rate_3_day4}</td>
      <td>{date_3_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">对照组</td>
      <td rowspan="4">{qua_control}</td>
      <td>{net_user_count_control_day1}</td>
      <td>{popup_success_rate_control_day1}</td>
      <td>{cloud_game_plugin_launch_success_rate_control_day1}</td>
      <td>{date_control_day1}</td>
    </tr>
    <tr>
      <td>{net_user_count_control_day2}</td>
      <td>{popup_success_rate_control_day2}</td>
      <td>{cloud_game_plugin_launch_success_rate_control_day2}</td>
      <td>{date_control_day2}</td>
    </tr>
    <tr>
      <td>{net_user_count_control_day3}</td>
      <td>{popup_success_rate_control_day3}</td>
      <td>{cloud_game_plugin_launch_success_rate_control_day3}</td>
      <td>{date_control_day3}</td>
    </tr>
    <tr>
      <td>{net_user_count_control_day4}</td>
      <td>{popup_success_rate_control_day4}</td>
      <td>{cloud_game_plugin_launch_success_rate_control_day4}</td>
      <td>{date_control_day4}</td>
    </tr>
  </tbody>
</table>
"""
