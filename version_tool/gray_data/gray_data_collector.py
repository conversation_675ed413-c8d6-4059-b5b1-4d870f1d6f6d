#!/usr/bin/env python3
"""
灰度数据收集器
提供清晰、模块化的数据收集、验证和报告生成功能
"""

import sys
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from abc import ABC, abstractmethod

from common.client.beacon_client import BeaconClient
from common.client.bugly_client import BuglyClient

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from version_tool.gray_data.gray_data_api import GrayDataParams, create_gray_data_api
from version_tool.gray_data.data_processors import (
    CrashAnrProcessor, LaunchSpeedProcessor, DownloadProcessor, OtherDataProcessor, AdDataProcessor
)
from version_tool.gray_data.agent.ai_anomaly_summary_agent import create_ai_anomaly_summary_agent


@dataclass
class CollectorConfig:
    """收集器配置类 - 集中管理阈值和配置"""

    # 异常检测阈值
    crash_rate_threshold: float = 0.0018  # 0.18%
    anr_rate_threshold: float = 0.0005    # 0.05%
    launch_speed_threshold: float = 50.0   # 50ms
    download_rate_threshold: float = 0.7   # 0.7%
    cvr_threshold: float = 1.0             # 1%
    ad_click_threshold: float = 0.1        # 0.1%
    ad_download_threshold: float = 10.0    # 10%
    ad_install_threshold: float = 10.0     # 10%
    popup_rate_threshold: float = 1.0      # 1%
    cloud_game_threshold: float = 97.0     # 97%

    # 精度控制常量
    precision_tolerance: float = 0.0001    # 小差异显示精确值的阈值
    float_comparison_epsilon: float = 1e-6  # 浮点数比较容差

    # 数据结构配置
    target_groups: int = 4
    target_days: int = 4
    default_value: str = "/"


@dataclass
class AnomalyRecord:
    """异常记录数据类"""
    user_type: str
    metric_type: str
    group_name: str
    day: str
    value: str
    reason: str
    qua_version: str = "/"
    specific_date: str = "/"


class DataProcessor(ABC):
    """数据处理器抽象基类"""

    def __init__(self, config: CollectorConfig):
        self.config = config

    @abstractmethod
    def process_data(self, raw_data: Any, versions: List[str], user_type: str, test_dates: List[str]) -> Any:
        """处理数据的抽象方法"""
        pass

    def _find_control_group_index(self, versions: List[str], raw_data: List) -> Optional[int]:
        """查找对照组索引"""
        for i in range(len(versions) - 1, -1, -1):
            if versions[i] != "/" and (i >= len(raw_data) or raw_data[i] is not None):
                return i
        return None


class AnomalyDetector:
    """异常检测器 - 统一的异常检测逻辑"""

    def __init__(self, config: CollectorConfig):
        self.config = config
        self.anomaly_records: List[AnomalyRecord] = []

    def record_anomaly(self, user_type: str, metric_type: str, group_name: str,
                      day: str, value: str, reason: str, qua_version: str = None,
                      specific_date: str = None) -> None:
        """记录异常数据"""
        record = AnomalyRecord(
            user_type=user_type,
            metric_type=metric_type,
            group_name=group_name,
            day=day,
            value=value,
            reason=reason,
            qua_version=qua_version or "/",
            specific_date=specific_date or "/"
        )
        self.anomaly_records.append(record)

        # 构建显示信息
        extra_info_parts = []
        if qua_version and qua_version != "/":
            extra_info_parts.append(f"QUA: {qua_version}")
        if specific_date and specific_date != "/":
            extra_info_parts.append(f"时间: {specific_date}")

        extra_info = f" [{', '.join(extra_info_parts)}]" if extra_info_parts else ""
        print(f"         记录异常数据: {user_type}用户 {group_name} {day} {metric_type} = {value} ({reason}){extra_info}")

    def get_anomaly_summary(self) -> str:
        """
        生成异常数据汇总报告

        返回格式化的异常数据汇总，按指标类型和用户类型分组显示
        包含详细的异常原因和上下文信息
        """
        if not self.anomaly_records:
            return self._generate_no_anomaly_message()

        # 数据预处理和分组
        grouped_data = self._group_anomaly_records()

        # 生成汇总报告
        summary_sections = []

        # 按指标类型生成各个部分
        for metric_type in self._get_metric_display_order():
            if metric_type in grouped_data:
                section = self._generate_metric_section(metric_type, grouped_data[metric_type])
                if section:
                    summary_sections.append(section)

        # 用双换行符连接各个部分，确保适当的间距
        return "\n\n".join(summary_sections)
    


    def _generate_no_anomaly_message(self) -> str:
        """生成无异常数据的消息"""
        return "未发现异常数据"

    def _group_anomaly_records(self) -> Dict[str, List[AnomalyRecord]]:
        """按指标类型分组异常记录"""
        grouped = {}
        for record in self.anomaly_records:
            if record.metric_type not in grouped:
                grouped[record.metric_type] = []
            grouped[record.metric_type].append(record)
        return grouped

    def _get_metric_display_order(self) -> List[str]:
        """获取指标显示顺序"""
        return [
            'crash',        # 崩溃率
            'anr',          # ANR率
            'launch_speed', # 启动速度
            'download',     # 下载率
            'cvr',          # 转化率
            'ad',           # 广告转化
            'popup',        # 弹窗成功率
            'cloud_game'    # 云游戏成功率
        ]

    def _get_metric_display_name(self, metric_type: str) -> str:
        """获取指标显示名称"""
        metric_names = {
            'crash': 'Crash率异常',
            'anr': 'ANR率异常',
            'launch_speed': '启动速度异常',
            'download': '外call下载率异常',
            'cvr': '下载安装CVR异常',
            'ad': '广告转化率异常',
            'popup': '弹窗成功率异常',
            'cloud_game': '云游戏插件拉起成功率异常'
        }
        return metric_names.get(metric_type, f"{metric_type}异常")



    def _generate_metric_section(self, metric_type: str, records: List[AnomalyRecord]) -> str:
        """生成单个指标的异常报告部分"""
        if not records:
            return ""

        section_lines = []
        metric_name = self._get_metric_display_name(metric_type)
        section_lines.append(f"## {metric_name}")

        # 按用户类型分组
        new_user_records = [r for r in records if r.user_type == 'new']
        old_user_records = [r for r in records if r.user_type == 'old']

        # 生成新用户部分
        if new_user_records:
            section_lines.extend(self._generate_user_type_section("新用户", new_user_records))

        # 生成老用户部分
        if old_user_records:
            if new_user_records:  # 如果前面有新用户数据，添加分隔
                section_lines.append("")
            section_lines.extend(self._generate_user_type_section("老用户", old_user_records))

        return "\n".join(section_lines)

    def _generate_user_type_section(self, user_type_name: str, records: List[AnomalyRecord]) -> List[str]:
        """生成特定用户类型的异常记录部分"""
        if not records:
            return []

        section_lines = [f"**{user_type_name}：**"]

        # 按组名和日期排序，确保显示顺序一致
        sorted_records = sorted(records, key=lambda r: (r.group_name, r.day))

        for record in sorted_records:
            # 构建记录行
            record_line = self._format_anomaly_record(record)
            section_lines.append(record_line)

        return section_lines

    def _format_anomaly_record(self, record: AnomalyRecord) -> str:
        """格式化单条异常记录"""
        # 基础信息
        base_info = f"- **{record.group_name}** {record.day}: `{record.value}` ({record.reason})"

        # 额外信息
        extra_info = self._build_extra_info(record)

        return f"{base_info}{extra_info}"

    def _build_extra_info(self, record: AnomalyRecord) -> str:
        """构建额外信息字符串"""
        extra_info_parts = []

        # QUA版本信息
        if record.qua_version and record.qua_version not in ["/", "N/A", ""]:
            extra_info_parts.append(f"QUA: {record.qua_version}")

        # 特定日期信息
        if record.specific_date and record.specific_date not in ["/", "N/A", ""]:
            extra_info_parts.append(f"时间: {record.specific_date}")

        # 返回格式化的额外信息
        if extra_info_parts:
            return f" `[{', '.join(extra_info_parts)}]`"
        return ""


class DataUtils:
    """数据处理工具类"""

    @staticmethod
    def format_date_for_api(date_str: str) -> str:
        """将日期格式从YYYY/MM/DD转换为YYYY/M/D（去掉前导0）"""
        if date_str == "/":
            return "/"
        date_parts = date_str.split('/')
        return f"{date_parts[0]}/{int(date_parts[1])}/{int(date_parts[2])}"

    @staticmethod
    def ensure_matrix_data(data_list: List[List[str]], target_groups: int = 4,
                          target_days: int = 4, default_value: str = "/") -> List[List[str]]:
        """确保数据为指定组数x天数的矩阵结构"""
        while len(data_list) < target_groups:
            data_list.append([default_value] * target_days)

        for group in data_list:
            while len(group) < target_days:
                group.append(default_value)

        return data_list

    @staticmethod
    def normalize_date_format(date_str: str) -> str:
        """标准化日期格式，将各种格式统一为 YYYY-MM-DD"""
        if not date_str:
            return date_str

        # 处理 YYYY/M/D 或 YYYY/MM/DD 格式
        if '/' in date_str:
            parts = date_str.split('/')
            if len(parts) == 3:
                year, month, day = parts
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"

        # 处理 YYYY-M-D 或 YYYY-MM-DD 格式
        if '-' in date_str:
            parts = date_str.split('-')
            if len(parts) == 3:
                year, month, day = parts
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"

        return date_str

    @staticmethod
    def validate_data_alignment(data_dict: Dict[str, Any], expected_dates: List[str],
                               expected_quas: List[str]) -> List[str]:
        """验证收集的数据是否与预期的日期和QUA版本对齐"""
        missing_data = []

        # 标准化所有日期格式
        available_dates_raw = set(data_dict.keys())

        # 创建原始日期到标准化日期的映射
        raw_to_normalized = {date: DataUtils.normalize_date_format(date) for date in available_dates_raw}
        normalized_to_raw = {normalized: raw for raw, normalized in raw_to_normalized.items()}

        # 检查每个日期的QUA版本覆盖
        for expected_date in expected_dates:
            expected_date_normalized = DataUtils.normalize_date_format(expected_date)

            # 找到对应的原始日期格式
            raw_date = normalized_to_raw.get(expected_date_normalized)

            if raw_date and raw_date in data_dict:
                available_quas = set(data_dict[raw_date].keys())
                expected_quas_set = set(qua for qua in expected_quas if qua != "/")
                missing_quas = expected_quas_set - available_quas

                if missing_quas:
                    for qua in missing_quas:
                        missing_data.append(f"日期={expected_date_normalized}, QUA={qua}")
            else:
                for qua in expected_quas:
                    if qua != "/":
                        missing_data.append(f"日期={expected_date_normalized}, QUA={qua}")

        return missing_data


class GrayDataCollector:
    """灰度数据收集器"""

    def __init__(self, config: Optional[CollectorConfig] = None):
        """初始化数据收集器"""
        self.config = config or CollectorConfig()
        self.bugly_client = BuglyClient()
        self.beacon_client = BeaconClient()
        self.gray_data = GrayDataParams()
        self.anomaly_detector = AnomalyDetector(self.config)
        self.ai_agent = create_ai_anomaly_summary_agent()

        # 初始化数据处理器
        self.crash_anr_processor = CrashAnrProcessor(self.config, self.anomaly_detector)
        self.launch_speed_processor = LaunchSpeedProcessor(self.config, self.anomaly_detector)
        self.download_processor = DownloadProcessor(self.config, self.anomaly_detector)
        self.other_data_processor = OtherDataProcessor(self.config, self.anomaly_detector)
        self.ad_data_processor = AdDataProcessor(self.config, self.anomaly_detector)

    def collect_gray_data(self, config: Dict[str, Any]) -> GrayDataParams:
        """收集灰度数据 - 主入口方法"""
        print("开始收集灰度数据...")
        print("=" * 60)

        # 设置基础配置
        self._setup_configuration(config)

        # 收集各类数据
        self._collect_all_data(config)

        # 验证数据一致性
        data_consistency_ok = self._validate_final_data_consistency(config)

        # 输出收集总结
        self._print_collection_summary(data_consistency_ok)

        return self.gray_data

    def collect_gray_data_test_crash_anr_only(self, config: Dict[str, Any]) -> GrayDataParams:
        """测试版本：仅收集crash和anr数据"""
        print("开始收集灰度数据 [测试模式 - 仅crash和anr]...")
        print("=" * 60)

        # 设置基础配置
        self._setup_configuration(config)

        # 收集crash和anr数据
        self._collect_all_data_test_crash_anr_only(config)

        # 验证数据一致性（简化版）
        data_consistency_ok = self._validate_final_data_consistency_test_crash_anr_only(config)

        # 输出收集总结（简化版）
        self._print_collection_summary_test_crash_anr_only(data_consistency_ok)

        return self.gray_data

    def get_anomaly_summary(self) -> str:
        """获取异常数据汇总"""
        return self.anomaly_detector.get_anomaly_summary()
    
    def get_ai_anomaly_summary(self, anomaly_summary: str) -> str:
        """获取AI异常总结"""
        return self.ai_agent.generate_ai_summary(anomaly_summary)

    def _setup_configuration(self, config: Dict[str, Any]) -> None:
        """设置基础配置"""
        print("设置基础配置...")
        self._set_qua_versions(config)
        self._set_test_dates(config)
        print("基础配置设置完成")

    def _collect_all_data(self, config: Dict[str, Any]) -> None:
        """收集所有数据"""
        print("\n开始数据收集...")

        # 收集新用户数据
        self._collect_new_user_data(config['new_user'])

        # 收集老用户数据
        self._collect_old_user_data(config['old_user'])

        print("\n所有数据收集完成")

    def _collect_all_data_test_crash_anr_only(self, config: Dict[str, Any]) -> None:
        """测试版本：仅收集crash和anr数据"""
        print("\n开始数据收集 [测试模式 - 仅crash和anr]...")

        # 收集新用户数据（仅crash和anr）
        self._collect_new_user_data_test_crash_anr_only(config['new_user'])

        # 收集老用户数据（仅crash和anr）
        self._collect_old_user_data_test_crash_anr_only(config['old_user'])

        print("\n[测试模式] crash和anr数据收集完成")

    def _collect_new_user_data(self, new_user_config: Dict[str, Any]) -> None:
        """收集新用户数据"""
        print("\n收集新用户数据...")
        print("-" * 40)

        # 创建列表副本，避免修改原始配置
        qua_versions = new_user_config['qua_versions'].copy()
        rqd_versions = new_user_config['rqd_versions'].copy()
        test_dates = new_user_config['test_dates'].copy()

        # 使用专门的处理器收集数据
        self._collect_user_data_with_processors('new', qua_versions, rqd_versions, test_dates)
        print("新用户数据收集完成")

    def _collect_old_user_data(self, old_user_config: Dict[str, Any]) -> None:
        """收集老用户数据"""
        print("\n收集老用户数据...")
        print("-" * 40)

        # 创建列表副本，避免修改原始配置
        qua_versions = old_user_config['qua_versions'].copy()
        rqd_versions = old_user_config['rqd_versions'].copy()
        test_dates = old_user_config['test_dates'].copy()

        # 使用专门的处理器收集数据
        self._collect_user_data_with_processors('old', qua_versions, rqd_versions, test_dates)
        print("老用户数据收集完成")

    def _collect_new_user_data_test_crash_anr_only(self, new_user_config: Dict[str, Any]) -> None:
        """测试版本：仅收集新用户crash和anr数据"""
        print("\n[测试模式] 收集新用户数据...")
        print("-" * 40)

        # 创建列表副本，避免修改原始配置
        qua_versions = new_user_config['qua_versions'].copy()
        rqd_versions = new_user_config['rqd_versions'].copy()
        test_dates = new_user_config['test_dates'].copy()

        # 使用测试版本的处理器收集数据
        self._collect_user_data_with_processors_test_crash_anr_only('new', qua_versions, rqd_versions, test_dates)
        print("[测试模式] 新用户crash和anr数据收集完成")

    def _collect_old_user_data_test_crash_anr_only(self, old_user_config: Dict[str, Any]) -> None:
        """测试版本：仅收集老用户crash和anr数据"""
        print("\n[测试模式] 收集老用户数据...")
        print("-" * 40)

        # 创建列表副本，避免修改原始配置
        qua_versions = old_user_config['qua_versions'].copy()
        rqd_versions = old_user_config['rqd_versions'].copy()
        test_dates = old_user_config['test_dates'].copy()

        # 使用测试版本的处理器收集数据
        self._collect_user_data_with_processors_test_crash_anr_only('old', qua_versions, rqd_versions, test_dates)
        print("[测试模式] 老用户crash和anr数据收集完成")

    def _collect_user_data_with_processors(self, user_type: str, qua_versions: List[str],
                                         rqd_versions: List[str], test_dates: List[str]) -> None:
        """使用处理器收集用户数据"""
        # 创建所有列表的深拷贝，防止被修改和污染
        import copy
        qua_versions_copy = copy.deepcopy(qua_versions)
        rqd_versions_copy = copy.deepcopy(rqd_versions)
        test_dates_copy = copy.deepcopy(test_dates)

        # 确保版本列表长度正确，防止污染
        qua_versions_copy = qua_versions_copy[:self.config.target_groups]
        rqd_versions_copy = rqd_versions_copy[:self.config.target_groups]

        # 验证输入数据的完整性
        print(f"    输入验证: QUA版本数={len(qua_versions_copy)}, RQD版本数={len(rqd_versions_copy)}, 测试日期数={len(test_dates_copy)}")

        # 输出调试信息
        if len(qua_versions) > self.config.target_groups:
            print(f"    警告: {user_type}用户QUA版本列表被截取，原始长度: {len(qua_versions)}, 截取后长度: {len(qua_versions_copy)}")
        if len(rqd_versions) > self.config.target_groups:
            print(f"    警告: {user_type}用户RQD版本列表被截取，原始长度: {len(rqd_versions)}, 截取后长度: {len(rqd_versions_copy)}")

        # 使用专门的处理器收集数据
        crash_data, anr_data = self.crash_anr_processor.process_crash_anr_data(
            rqd_versions_copy, test_dates_copy, user_type, self.bugly_client
        )

        launch_data = self.launch_speed_processor.process_launch_speed_data(
            qua_versions_copy, test_dates_copy, user_type, self.beacon_client
        )

        download_data = self.download_processor.process_download_data(
            qua_versions_copy, test_dates_copy, user_type, self.beacon_client
        )

        ad_data = self.ad_data_processor.process_ad_data(
            qua_versions_copy, test_dates_copy, self.beacon_client, user_type
        )

        other_data = self.other_data_processor.process_other_data(
            qua_versions_copy, test_dates_copy, rqd_versions_copy, self.beacon_client, user_type
        )

        # 验证处理后的数据结构
        self._validate_processed_data_structure(crash_data, anr_data, launch_data, user_type)

        # 根据用户类型设置数据
        if user_type == 'new':
            self._set_new_user_data(crash_data, anr_data, launch_data, download_data, other_data, ad_data)
        else:
            self._set_old_user_data(crash_data, anr_data, launch_data, download_data, other_data, ad_data)

    def _collect_user_data_with_processors_test_crash_anr_only(self, user_type: str, qua_versions: List[str],
                                         rqd_versions: List[str], test_dates: List[str]) -> None:
        """测试版本：仅收集crash和anr数据"""
        # 创建所有列表的深拷贝，防止被修改和污染
        import copy
        rqd_versions_copy = copy.deepcopy(rqd_versions)
        test_dates_copy = copy.deepcopy(test_dates)

        # qua_versions在测试模式下不使用，但保留参数以保持接口一致性
        _ = qua_versions  # 抑制未使用参数警告

        # 验证输入数据的完整性
        print(f"    [测试模式] 输入验证: RQD版本数={len(rqd_versions_copy)}, 测试日期数={len(test_dates_copy)}")
        print(f"    [测试模式] 仅收集crash和anr数据...")

        # 仅使用crash_anr_processor收集数据
        crash_data, anr_data = self.crash_anr_processor.process_crash_anr_data(
            rqd_versions_copy, test_dates_copy, user_type, self.bugly_client
        )

        # 创建空的其他数据结构用于占位
        empty_launch_data = {
            'regular_hot': [["/"] * 4] * 4,
            'regular_cold': [["/"] * 4] * 4,
            'outcall_hot': [["/"] * 4] * 4,
            'outcall_cold': [["/"] * 4] * 4
        }

        empty_download_data = {
            'start_rates': [["/"] * 4] * 4,
            'success_rates': [["/"] * 4] * 4,
            'cvr_rates': [["/"] * 4] * 4
        }

        empty_ad_data = {
            'ad_exposure': [["/"] * 4] * 4,
            'ad_click': [["/"] * 4] * 4,
            'ad_download': [["/"] * 4] * 4,
            'ad_install': [["/"] * 4] * 4,
            'click_ratio': [["/"] * 4] * 4,
            'download_ratio': [["/"] * 4] * 4,
            'install_ratio': [["/"] * 4] * 4
        }

        empty_other_data = {
            'net_users': [["/"] * 4] * 4,
            'popup_rate': [["/"] * 4] * 4,
            'cloud_game_rate': [["/"] * 4] * 4
        }

        # 验证处理后的数据结构（仅验证crash和anr）
        self._validate_processed_data_structure_test_crash_anr_only(crash_data, anr_data, user_type)

        # 根据用户类型设置数据
        if user_type == 'new':
            self._set_new_user_data(crash_data, anr_data, empty_launch_data, empty_download_data, empty_other_data, empty_ad_data)
        else:
            self._set_old_user_data(crash_data, anr_data, empty_launch_data, empty_download_data, empty_other_data, empty_ad_data)

    def _validate_processed_data_structure(self, crash_data, anr_data, launch_data, user_type: str) -> None:
        """验证处理后的数据结构是否正确"""
        expected_groups = self.config.target_groups

        # 验证crash和ANR数据结构
        if crash_data and len(crash_data) != expected_groups:
            print(f"    警告: {user_type}用户crash数据结构异常: 期望{expected_groups}组，实际{len(crash_data)}组")
        if anr_data and len(anr_data) != expected_groups:
            print(f"    警告: {user_type}用户ANR数据结构异常: 期望{expected_groups}组，实际{len(anr_data)}组")

        # 验证启动速度数据结构
        if launch_data:
            for key, data in launch_data.items():
                if data and len(data) != expected_groups:
                    print(f"    警告: {user_type}用户{key}数据结构异常: 期望{expected_groups}组，实际{len(data)}组")
                    # 检查是否包含HTML格式的异常数据
                    for i, item in enumerate(data):
                        if isinstance(item, str) and '<span' in item:
                            print(f"    检查: 发现HTML格式数据污染: 组{i+1} = {item[:100]}...")

    def _validate_processed_data_structure_test_crash_anr_only(self, crash_data, anr_data, user_type: str) -> None:
        """测试版本：仅验证crash和anr数据结构"""
        expected_groups = self.config.target_groups

        # 验证crash和ANR数据结构
        if crash_data and len(crash_data) != expected_groups:
            print(f"    [测试模式] 警告: {user_type}用户crash数据结构异常: 期望{expected_groups}组，实际{len(crash_data)}组")
        else:
            print(f"    [测试模式] {user_type}用户crash数据结构正常: {len(crash_data)}组")

        if anr_data and len(anr_data) != expected_groups:
            print(f"    [测试模式] 警告: {user_type}用户ANR数据结构异常: 期望{expected_groups}组，实际{len(anr_data)}组")
        else:
            print(f"    [测试模式] {user_type}用户ANR数据结构正常: {len(anr_data)}组")

        # 输出数据样本用于验证
        if crash_data:
            print(f"    [测试模式] crash数据样本: 第1组前2天 = {crash_data[0][:2] if crash_data[0] else 'N/A'}")
        if anr_data:
            print(f"    [测试模式] ANR数据样本: 第1组前2天 = {anr_data[0][:2] if anr_data[0] else 'N/A'}")

    def _set_new_user_data(self, crash_data, anr_data, launch_data, download_data, other_data, ad_data):
        """设置新用户数据"""
        self.gray_data.new_device_crash = DataUtils.ensure_matrix_data(crash_data)
        self.gray_data.new_anr_rate = DataUtils.ensure_matrix_data(anr_data)

        self.gray_data.new_regular_hot = launch_data['regular_hot']
        self.gray_data.new_regular_cold = launch_data['regular_cold']
        self.gray_data.new_outcall_hot = launch_data['outcall_hot']
        self.gray_data.new_outcall_cold = launch_data['outcall_cold']

        self.gray_data.new_download_start = download_data['start_rates']
        self.gray_data.new_download_success = download_data['success_rates']
        self.gray_data.new_download_cvr = download_data['cvr_rates']

        self.gray_data.new_ad_exposure = ad_data['ad_exposure']
        self.gray_data.new_ad_click = ad_data['ad_click']
        self.gray_data.new_ad_download = ad_data['ad_download']
        self.gray_data.new_ad_install = ad_data['ad_install']
        self.gray_data.new_click_ratio = ad_data['click_ratio']
        self.gray_data.new_download_ratio = ad_data['download_ratio']
        self.gray_data.new_install_ratio = ad_data['install_ratio']
        self.gray_data.new_net_users = other_data['net_users']
        self.gray_data.new_popup_rate = other_data['popup_rate']
        self.gray_data.new_cloud_game_rate = other_data['cloud_game_rate']

    def _set_old_user_data(self, crash_data, anr_data, launch_data, download_data, other_data, ad_data):
        """设置老用户数据"""
        self.gray_data.old_device_crash = DataUtils.ensure_matrix_data(crash_data)
        self.gray_data.old_anr_rate = DataUtils.ensure_matrix_data(anr_data)

        self.gray_data.old_regular_hot = launch_data['regular_hot']
        self.gray_data.old_regular_cold = launch_data['regular_cold']
        self.gray_data.old_outcall_hot = launch_data['outcall_hot']
        self.gray_data.old_outcall_cold = launch_data['outcall_cold']

        self.gray_data.old_download_start = download_data['start_rates']
        self.gray_data.old_download_success = download_data['success_rates']
        self.gray_data.old_download_cvr = download_data['cvr_rates']

        self.gray_data.old_ad_exposure = ad_data['ad_exposure']
        self.gray_data.old_ad_click = ad_data['ad_click']
        self.gray_data.old_ad_download = ad_data['ad_download']
        self.gray_data.old_ad_install = ad_data['ad_install']
        self.gray_data.old_click_ratio = ad_data['click_ratio']
        self.gray_data.old_download_ratio = ad_data['download_ratio']
        self.gray_data.old_install_ratio = ad_data['install_ratio']
        self.gray_data.old_net_users = other_data['net_users']
        self.gray_data.old_popup_rate = other_data['popup_rate']
        self.gray_data.old_cloud_game_rate = other_data['cloud_game_rate']

    def _set_qua_versions(self, config: Dict[str, Any]) -> None:
        """设置QUA版本信息"""
        new_qua = config['new_user']['qua_versions'].copy()
        old_qua = config['old_user']['qua_versions'].copy()

        # 确保有4个值，不足的用"/"补齐，多余的截取前4个
        new_qua_padded = (new_qua + ["/"] * self.config.target_groups)[:self.config.target_groups]
        old_qua_padded = (old_qua + ["/"] * self.config.target_groups)[:self.config.target_groups]

        (self.gray_data.qua_exp1, self.gray_data.qua_exp2,
         self.gray_data.qua_exp3, self.gray_data.qua_control) = new_qua_padded
        (self.gray_data.qua_old_exp1, self.gray_data.qua_old_exp2,
         self.gray_data.qua_old_exp3, self.gray_data.qua_old_control) = old_qua_padded

    def _set_test_dates(self, config: Dict[str, Any]) -> None:
        """设置测试日期"""
        new_dates = config['new_user']['test_dates'].copy()
        old_dates = config['old_user']['test_dates'].copy()

        # 补齐到指定天数
        self.gray_data.test_dates = new_dates + ["/"] * (self.config.target_days - len(new_dates))
        self.gray_data.test_dates_old = old_dates + ["/"] * (self.config.target_days - len(old_dates))



    def _validate_final_data_consistency(self, config: Dict[str, Any]) -> bool:
        """验证最终数据的一致性和完整性"""
        print("\n验证最终数据一致性...")
        print("=" * 50)

        issues = []

        # 验证QUA版本配置一致性 - 使用深拷贝避免引用问题
        expected_new_quas = config['new_user']['qua_versions'].copy()
        actual_new_quas = [self.gray_data.qua_exp1, self.gray_data.qua_exp2, self.gray_data.qua_exp3, self.gray_data.qua_control]

        expected_old_quas = config['old_user']['qua_versions'].copy()
        actual_old_quas = [self.gray_data.qua_old_exp1, self.gray_data.qua_old_exp2, self.gray_data.qua_old_exp3, self.gray_data.qua_old_control]

        # 只比较前4个元素，避免列表污染问题
        expected_new_quas_trimmed = expected_new_quas[:4] if len(expected_new_quas) >= 4 else expected_new_quas
        expected_old_quas_trimmed = expected_old_quas[:4] if len(expected_old_quas) >= 4 else expected_old_quas

        if expected_new_quas_trimmed != actual_new_quas:
            issues.append(f"新用户QUA版本不一致: 期望={expected_new_quas_trimmed}, 实际={actual_new_quas}")

        if expected_old_quas_trimmed != actual_old_quas:
            issues.append(f"老用户QUA版本不一致: 期望={expected_old_quas_trimmed}, 实际={actual_old_quas}")
            # 如果原始列表被污染，输出调试信息
            if len(expected_old_quas) > 4:
                print(f"警告: 检测到老用户QUA版本列表被污染，原始长度: {len(expected_old_quas)}, 前10个元素: {expected_old_quas[:10]}")

        # 验证测试日期一致性
        expected_new_dates = config['new_user']['test_dates']
        actual_new_dates = self.gray_data.test_dates[:len(expected_new_dates)]  # 只比较有效日期

        expected_old_dates = config['old_user']['test_dates']
        actual_old_dates = self.gray_data.test_dates_old[:len(expected_old_dates)]

        if expected_new_dates != actual_new_dates:
            issues.append(f"新用户测试日期不一致: 期望={expected_new_dates}, 实际={actual_new_dates}")

        if expected_old_dates != actual_old_dates:
            issues.append(f"老用户测试日期不一致: 期望={expected_old_dates}, 实际={actual_old_dates}")

        # 验证矩阵数据结构完整性
        matrix_fields = [
            ('new_device_crash', '新用户设备crash率'),
            ('old_device_crash', '老用户设备crash率'),
            ('new_anr_rate', '新用户ANR率'),
            ('old_anr_rate', '老用户ANR率'),
            ('new_download_start', '新用户下载开始率'),
            ('old_download_start', '老用户下载开始率'),
            ('new_ad_exposure', '新用户广告曝光'),
            ('old_ad_exposure', '老用户广告曝光')
        ]

        # 计算实际的测试天数（基于实际API返回的数据）
        actual_test_days = len(expected_new_dates) if expected_new_dates else 4

        for field_name, field_desc in matrix_fields:
            field_data = getattr(self.gray_data, field_name)
            if not field_data or len(field_data) != 4:
                issues.append(f"{field_desc}数据结构错误: 应为4组，实际为{len(field_data) if field_data else 0}组")
            else:
                for i, group_data in enumerate(field_data):
                    if not group_data:
                        issues.append(f"{field_desc}第{i+1}组数据错误: 数据为空")
                    elif len(group_data) != actual_test_days:
                        # 只有当实际天数与期望天数差异较大时才报告错误
                        if abs(len(group_data) - actual_test_days) > 1:
                            issues.append(f"{field_desc}第{i+1}组数据错误: 应为{actual_test_days}天，实际为{len(group_data)}天")

        # 验证启动速度数据完整性
        speed_fields = [
            ('new_regular_hot', '新用户常规热启动'),
            ('new_regular_cold', '新用户常规冷启动'),
            ('old_regular_hot', '老用户常规热启动'),
            ('old_regular_cold', '老用户常规冷启动')
        ]

        for field_name, field_desc in speed_fields:
            field_data = getattr(self.gray_data, field_name)
            if not field_data or len(field_data) != 4:
                issues.append(f"{field_desc}数据错误: 应为4组，实际为{len(field_data) if field_data else 0}组")

        # 输出验证结果
        if issues:
            print("发现数据一致性问题:")
            for i, issue in enumerate(issues, 1):
                print(f"   {i}. {issue}")
            print(f"\n总计发现 {len(issues)} 个问题")
            return False
        else:
            print("数据一致性验证通过")
            return True

    def _print_collection_summary(self, data_consistency_ok: bool) -> None:
        """打印数据收集总结"""
        print("\n" + "=" * 60)
        print("灰度数据收集完成！")
        print("=" * 60)

        # 打印数据收集总结
        print("数据收集总结:")
        print(f"  新用户设备crash率: {len(self.gray_data.new_device_crash)}组 x {len(self.gray_data.new_device_crash[0])}天")
        print(f"  老用户设备crash率: {len(self.gray_data.old_device_crash)}组 x {len(self.gray_data.old_device_crash[0])}天")
        print(f"  新用户启动速度: {len(self.gray_data.new_regular_hot)}组")
        print(f"  老用户启动速度: {len(self.gray_data.old_regular_hot)}组")
        print(f"  新用户下载数据: {len(self.gray_data.new_download_start)}组 x {len(self.gray_data.new_download_start[0])}天")
        print(f"  老用户下载数据: {len(self.gray_data.old_download_start)}组 x {len(self.gray_data.old_download_start[0])}天")
        print(f"  新用户广告数据: {len(self.gray_data.new_ad_exposure)}组 x {len(self.gray_data.new_ad_exposure[0])}天")
        print(f"  老用户广告数据: {len(self.gray_data.old_ad_exposure)}组 x {len(self.gray_data.old_ad_exposure[0])}天")

        # 数据质量报告
        print(f"\n数据质量报告:")
        print(f"  数据一致性检查: {'通过' if data_consistency_ok else '失败'}")

        if not data_consistency_ok:
            print("  建议检查数据收集过程中的警告信息")

    def _validate_final_data_consistency_test_crash_anr_only(self, config: Dict[str, Any]) -> bool:
        """测试版本：验证crash和anr数据的一致性和完整性"""
        print("\n[测试模式] 验证crash和anr数据一致性...")
        print("=" * 50)

        issues = []

        # 验证测试日期一致性
        expected_new_dates = config['new_user']['test_dates']
        actual_new_dates = self.gray_data.test_dates[:len(expected_new_dates)]

        expected_old_dates = config['old_user']['test_dates']
        actual_old_dates = self.gray_data.test_dates_old[:len(expected_old_dates)]

        if expected_new_dates != actual_new_dates:
            issues.append(f"新用户测试日期不一致: 期望={expected_new_dates}, 实际={actual_new_dates}")

        if expected_old_dates != actual_old_dates:
            issues.append(f"老用户测试日期不一致: 期望={expected_old_dates}, 实际={actual_old_dates}")

        # 验证crash和anr数据结构完整性
        crash_anr_fields = [
            ('new_device_crash', '新用户设备crash率'),
            ('old_device_crash', '老用户设备crash率'),
            ('new_anr_rate', '新用户ANR率'),
            ('old_anr_rate', '老用户ANR率')
        ]

        # 计算实际的测试天数
        actual_test_days = len(expected_new_dates) if expected_new_dates else 4

        for field_name, field_desc in crash_anr_fields:
            field_data = getattr(self.gray_data, field_name)
            if not field_data or len(field_data) != 4:
                issues.append(f"{field_desc}数据结构错误: 应为4组，实际为{len(field_data) if field_data else 0}组")
            else:
                for i, group_data in enumerate(field_data):
                    if not group_data:
                        issues.append(f"{field_desc}第{i+1}组数据错误: 数据为空")
                    elif len(group_data) != actual_test_days:
                        if abs(len(group_data) - actual_test_days) > 1:
                            issues.append(f"{field_desc}第{i+1}组数据错误: 应为{actual_test_days}天，实际为{len(group_data)}天")

        # 输出验证结果
        if issues:
            print("[测试模式] 发现数据一致性问题:")
            for i, issue in enumerate(issues, 1):
                print(f"   {i}. {issue}")
            print(f"\n[测试模式] 总计发现 {len(issues)} 个问题")
            return False
        else:
            print("[测试模式] crash和anr数据一致性验证通过")
            return True

    def _print_collection_summary_test_crash_anr_only(self, data_consistency_ok: bool) -> None:
        """测试版本：打印crash和anr数据收集总结"""
        print("\n" + "=" * 60)
        print("[测试模式] crash和anr数据收集完成！")
        print("=" * 60)

        # 打印数据收集总结
        print("[测试模式] 数据收集总结:")
        print(f"  新用户设备crash率: {len(self.gray_data.new_device_crash)}组 x {len(self.gray_data.new_device_crash[0])}天")
        print(f"  老用户设备crash率: {len(self.gray_data.old_device_crash)}组 x {len(self.gray_data.old_device_crash[0])}天")
        print(f"  新用户ANR率: {len(self.gray_data.new_anr_rate)}组 x {len(self.gray_data.new_anr_rate[0])}天")
        print(f"  老用户ANR率: {len(self.gray_data.old_anr_rate)}组 x {len(self.gray_data.old_anr_rate[0])}天")

        # 数据质量报告
        print(f"\n[测试模式] 数据质量报告:")
        print(f"  数据一致性检查: {'通过' if data_consistency_ok else '失败'}")

        # 显示数据样本
        print(f"\n[测试模式] 数据样本:")
        if self.gray_data.new_device_crash and self.gray_data.new_device_crash[0]:
            print(f"  新用户crash率样本 (第1组前2天): {self.gray_data.new_device_crash[0][:2]}")
        if self.gray_data.new_anr_rate and self.gray_data.new_anr_rate[0]:
            print(f"  新用户ANR率样本 (第1组前2天): {self.gray_data.new_anr_rate[0][:2]}")
        if self.gray_data.old_device_crash and self.gray_data.old_device_crash[0]:
            print(f"  老用户crash率样本 (第1组前2天): {self.gray_data.old_device_crash[0][:2]}")
        if self.gray_data.old_anr_rate and self.gray_data.old_anr_rate[0]:
            print(f"  老用户ANR率样本 (第1组前2天): {self.gray_data.old_anr_rate[0][:2]}")

        if not data_consistency_ok:
            print("  [测试模式] 建议检查数据收集过程中的警告信息")







    def _process_download_comparison(self, raw_download_data: List, raw_cvr_data: List, qua_versions: List[str], user_type: str, test_dates: List[str]) -> Dict[str, List[List[str]]]:
        """处理下载数据，进行实验组与对照组比较"""
        result = {
            'start_rates': [],
            'success_rates': [],
            'cvr_rates': []
        }

        # 确定对照组索引（通常是最后一个非占位符版本）
        control_group_index = None
        for i in range(len(qua_versions) - 1, -1, -1):
            if qua_versions[i] != "/" and raw_download_data[i] is not None:
                control_group_index = i
                break

        if control_group_index is None:
            # 如果没有对照组，直接格式化所有数据
            for i, (download_data, cvr_data) in enumerate(zip(raw_download_data, raw_cvr_data)):
                if download_data is None:
                    result['start_rates'].append(["/"] * 4)
                    result['success_rates'].append(["/"] * 4)
                    result['cvr_rates'].append(["/"] * 4)
                else:
                    start_rates = []
                    success_rates = []
                    cvr_rates = []

                    for j in range(4):  # 4天数据
                        if j < len(download_data) and download_data[j] is not None:
                            start_rates.append(download_data[j]['start_rate']['formatted'])
                            success_rates.append(download_data[j]['success_rate']['formatted'])
                        else:
                            start_rates.append("/")
                            success_rates.append("/")

                        if j < len(cvr_data) and cvr_data[j] is not None:
                            cvr_rates.append(f"{cvr_data[j]['value']:.2f}%")
                        else:
                            cvr_rates.append("/")

                    result['start_rates'].append(start_rates)
                    result['success_rates'].append(success_rates)
                    result['cvr_rates'].append(cvr_rates)
            return result

        control_group_download = raw_download_data[control_group_index]
        control_group_cvr = raw_cvr_data[control_group_index]

        # 处理每个组的下载数据
        for i, (download_data, cvr_data) in enumerate(zip(raw_download_data, raw_cvr_data)):
            group_name = f"实验组{i+1}" if i < 3 else "对照组"

            if download_data is None:
                # 占位符组
                result['start_rates'].append(["/"] * 4)
                result['success_rates'].append(["/"] * 4)
                result['cvr_rates'].append(["/"] * 4)
                continue

            start_rates = []
            success_rates = []
            cvr_rates = []

            # 处理每天的数据
            for day_idx in range(4):  # 4天数据
                # 处理下载开始率
                if day_idx < len(download_data) and download_data[day_idx] is not None:
                    start_metrics = download_data[day_idx]['start_rate']
                    start_rate_value = start_metrics['value']
                    start_rate_formatted = start_metrics['formatted']
                    start_count = start_metrics['count']

                    # 如果是实验组，与对照组比较
                    if i != control_group_index and i < 3:  # 实验组（前3组）
                        if (day_idx < len(control_group_download) and
                            control_group_download[day_idx] is not None):
                            control_start_rate = control_group_download[day_idx]['start_rate']['value']

                            if control_start_rate > 0:
                                # 基于显示格式的数值进行比较，确保与用户看到的数据一致
                                # 下载率通常显示为百分比，如"1.23%"，所以我们基于百分比格式进行比较
                                start_rate_display_value = float(f"{start_rate_value * 100:.2f}")  # 转换为百分比并保留2位小数
                                control_start_rate_display_value = float(f"{control_start_rate * 100:.2f}")  # 转换为百分比并保留2位小数
                                rate_diff_display = control_start_rate_display_value - start_rate_display_value  # 对照组 - 实验组（百分比）

                                if rate_diff_display >= 0.7:  # 对照组比实验组高0.7%或更多，基于显示精度比较
                                    # 创建用于表格显示的HTML格式
                                    start_rate_formatted = f'<span style="color: red;">{start_rate_formatted}<br>({start_count}户)</span>'

                                    # 创建用于异常记录的纯文本格式（不包含HTML标签）
                                    anomaly_display_value = f'{start_rate_value * 100:.2f}% ({start_count}户)'

                                    # 记录异常数据
                                    group_name = f"实验组{i+1}" if i < 3 else "对照组"
                                    qua_version = qua_versions[i] if i < len(qua_versions) else "N/A"
                                    # 获取对应的测试日期
                                    specific_date = test_dates[day_idx] if day_idx < len(test_dates) else "/"
                                    self.anomaly_detector.record_anomaly(user_type, 'download', group_name, f'第{day_idx+1}天', anomaly_display_value,
                                                       f'比对照组低{rate_diff_display:.2f}%', qua_version=qua_version,
                                                       specific_date=specific_date)

                    start_rates.append(start_rate_formatted)

                    # 处理下载成功率（不进行比较，直接使用原始格式）
                    success_rate_formatted = download_data[day_idx]['success_rate']['formatted']
                    success_rates.append(success_rate_formatted)
                else:
                    start_rates.append("/")
                    success_rates.append("/")

                # 处理CVR数据（进行实验组与对照组比较）
                if day_idx < len(cvr_data) and cvr_data[day_idx] is not None:
                    cvr_metrics = cvr_data[day_idx]
                    cvr_value = cvr_metrics['value']
                    start_download_count = cvr_metrics['count']

                    cvr_formatted = f"{cvr_value:.2f}%"

                    # 如果是实验组，与对照组比较
                    if i != control_group_index and i < 3:  # 实验组（前3组）
                        if (day_idx < len(control_group_cvr) and
                            control_group_cvr[day_idx] is not None):
                            control_cvr_value = control_group_cvr[day_idx]['value']

                            if control_cvr_value > 0:
                                # 基于显示格式的数值进行比较，确保与用户看到的数据一致
                                cvr_display_value = float(f"{cvr_value:.2f}")  # 保留2位小数精度
                                control_cvr_display_value = float(f"{control_cvr_value:.2f}")  # 保留2位小数精度
                                cvr_diff = control_cvr_display_value - cvr_display_value  # 对照组 - 实验组

                                if cvr_diff >= 1.0:  # 对照组比实验组高1%或更多，基于显示精度比较
                                    # 创建用于表格显示的HTML格式
                                    cvr_formatted = f'<span style="color: red;">{cvr_formatted}<br>({start_download_count}数)</span>'

                                    # 创建用于异常记录的纯文本格式（不包含HTML标签）
                                    anomaly_display_value = f'{cvr_value:.2f}% ({start_download_count}数)'

                                    # 记录异常数据
                                    group_name = f"实验组{i+1}" if i < 3 else "对照组"
                                    qua_version = qua_versions[i] if i < len(qua_versions) else "N/A"
                                    # 获取对应的测试日期
                                    specific_date = test_dates[day_idx] if day_idx < len(test_dates) else "/"
                                    self.anomaly_detector.record_anomaly(user_type, 'cvr', group_name, f'第{day_idx+1}天', anomaly_display_value,
                                                       f'比对照组低{cvr_diff:.2f}%', qua_version=qua_version,
                                                       specific_date=specific_date)

                    cvr_rates.append(cvr_formatted)
                else:
                    cvr_rates.append("/")

            result['start_rates'].append(start_rates)
            result['success_rates'].append(success_rates)
            result['cvr_rates'].append(cvr_rates)

        return result

    @staticmethod
    def _normalize_date_format(date_str: str) -> str:
        """标准化日期格式，将各种格式统一为 YYYY-MM-DD"""
        if not date_str:
            return date_str

        # 处理 YYYY/M/D 或 YYYY/MM/DD 格式
        if '/' in date_str:
            parts = date_str.split('/')
            if len(parts) == 3:
                year, month, day = parts
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"

        # 处理 YYYY-M-D 或 YYYY-MM-DD 格式
        if '-' in date_str:
            parts = date_str.split('-')
            if len(parts) == 3:
                year, month, day = parts
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"

        return date_str

    @staticmethod
    def _validate_data_alignment(data_dict: Dict[str, Any], expected_dates: List[str], expected_quas: List[str], data_type: str = None) -> List[str]:
        """验证收集的数据是否与预期的日期和QUA版本对齐"""
        _ = data_type  
        missing_data = []

        # 标准化所有日期格式
        available_dates_raw = set(data_dict.keys())

        # 创建原始日期到标准化日期的映射
        raw_to_normalized = {date: GrayDataCollector._normalize_date_format(date) for date in available_dates_raw}
        normalized_to_raw = {normalized: raw for raw, normalized in raw_to_normalized.items()}

        # 检查每个日期的QUA版本覆盖
        for expected_date in expected_dates:
            expected_date_normalized = GrayDataCollector._normalize_date_format(expected_date)

            # 找到对应的原始日期格式
            raw_date = normalized_to_raw.get(expected_date_normalized)

            if raw_date and raw_date in data_dict:
                available_quas = set(data_dict[raw_date].keys())
                expected_quas_set = set(qua for qua in expected_quas if qua != "/")
                missing_quas = expected_quas_set - available_quas

                if missing_quas:
                    for qua in missing_quas:
                        missing_data.append(f"日期={expected_date_normalized}, QUA={qua}")
            else:
                for qua in expected_quas:
                    if qua != "/":
                        missing_data.append(f"日期={expected_date_normalized}, QUA={qua}")

        return missing_data




    def _process_ad_data(self, ad_data: Dict[str, Any], qua_versions: List[str], result: Dict[str, List[List[str]]], expected_dates: List[str]) -> None:
        """处理广告数据 - 支持实验组与对照组比较"""
        # 验证数据对齐
        self._validate_data_alignment(ad_data, expected_dates, qua_versions, "广告")

        # 第一轮：收集所有原始数据
        raw_ad_data = []
        for i, qua_version in enumerate(qua_versions):
            qua_raw_data = []
            for date in sorted(ad_data.keys()):
                if qua_version == "/" or qua_version not in ad_data[date]:
                    qua_raw_data.append(None)
                else:
                    version_data = ad_data[date][qua_version]
                    qua_raw_data.append({
                        'exposure': version_data.get('广告曝光', 0),
                        'click': version_data.get('广告点击', 0),
                        'download': version_data.get('广告下载', 0),
                        'install': version_data.get('广告安装', 0),
                        'click_ratio': version_data.get('点击曝光率', 0),
                        'download_ratio': version_data.get('下载点击率', 0),
                        'install_ratio': version_data.get('安装下载率', 0)
                    })
            raw_ad_data.append(qua_raw_data)

        # 第二轮：进行实验组与对照组比较，生成最终格式化数据
        self._process_ad_comparison(raw_ad_data, qua_versions, result, sorted(ad_data.keys()))

    def _process_ad_comparison(self, raw_ad_data: List[List], qua_versions: List[str], result: Dict[str, List[List[str]]], sorted_dates: List[str]) -> None:
        """处理广告数据，进行实验组与对照组比较"""
        # 确定对照组索引（通常是最后一个非占位符版本）
        control_group_index = None
        for i in range(len(qua_versions) - 1, -1, -1):
            if qua_versions[i] != "/" and raw_ad_data[i] is not None:
                control_group_index = i
                break

        if control_group_index is None:
            # 如果没有对照组，直接格式化所有数据
            for i, qua_data in enumerate(raw_ad_data):
                for day_idx, day_data in enumerate(qua_data):
                    if day_data is None:
                        result['ad_exposure'][i].append("/")
                        result['ad_click'][i].append("/")
                        result['ad_download'][i].append("/")
                        result['ad_install'][i].append("/")
                        result['click_ratio'][i].append("/")
                        result['download_ratio'][i].append("/")
                        result['install_ratio'][i].append("/")
                    else:
                        result['ad_exposure'][i].append(day_data['exposure'])
                        result['ad_click'][i].append(day_data['click'])
                        result['ad_download'][i].append(day_data['download'])
                        result['ad_install'][i].append(day_data['install'])
                        result['click_ratio'][i].append(f"{day_data['click_ratio']:.2f}%")
                        result['download_ratio'][i].append(f"{day_data['download_ratio']:.2f}%")
                        result['install_ratio'][i].append(f"{day_data['install_ratio']:.2f}%")
            return

        control_group_ad = raw_ad_data[control_group_index]

        # 处理每个组的广告数据
        for i, qua_data in enumerate(raw_ad_data):
            group_name = f"实验组{i+1}" if i < 3 else "对照组"

            if qua_data is None or all(day is None for day in qua_data):
                # 占位符组
                for _ in range(len(sorted_dates)):
                    result['ad_exposure'][i].append("/")
                    result['ad_click'][i].append("/")
                    result['ad_download'][i].append("/")
                    result['ad_install'][i].append("/")
                    result['click_ratio'][i].append("/")
                    result['download_ratio'][i].append("/")
                    result['install_ratio'][i].append("/")
                continue

            # 处理每天的数据
            for day_idx, (date, day_data) in enumerate(zip(sorted_dates, qua_data)):
                if day_data is None:
                    result['ad_exposure'][i].append("/")
                    result['ad_click'][i].append("/")
                    result['ad_download'][i].append("/")
                    result['ad_install'][i].append("/")
                    result['click_ratio'][i].append("/")
                    result['download_ratio'][i].append("/")
                    result['install_ratio'][i].append("/")
                    continue

                # 基础数据（不进行比较）
                exposure = day_data['exposure']
                click = day_data['click']
                download = day_data['download']
                install = day_data['install']

                # 转化率数据（需要进行比较）
                click_ratio = day_data['click_ratio']
                download_ratio = day_data['download_ratio']
                install_ratio = day_data['install_ratio']

                # 格式化转化率
                click_ratio_formatted = f"{click_ratio:.2f}%"
                download_ratio_formatted = f"{download_ratio:.2f}%"
                install_ratio_formatted = f"{install_ratio:.2f}%"

                # 如果是实验组，与对照组比较
                if i != control_group_index and i < 3:  # 实验组（前3组）
                    if (day_idx < len(control_group_ad) and
                        control_group_ad[day_idx] is not None):
                        control_day_data = control_group_ad[day_idx]

                        # 比较点击曝光率（阈值：0.1%）
                        control_click_ratio = control_day_data['click_ratio']
                        click_display_value = float(f"{click_ratio:.2f}")  # 保留2位小数精度
                        control_click_display_value = float(f"{control_click_ratio:.2f}")  # 保留2位小数精度
                        click_diff = control_click_display_value - click_display_value  # 对照组 - 实验组
                        if click_diff >= 0.1:  # 0.1%，基于显示精度比较
                            # 创建用于表格显示的HTML格式
                            click_ratio_formatted = f'<span style="color: red;">{click_ratio_formatted}</span>'

                            # 创建用于异常记录的纯文本格式（不包含HTML标签）
                            anomaly_display_value = f'{click_ratio:.2f}%'

                            # 记录异常数据
                            group_name = f"实验组{i+1}" if i < 3 else "对照组"
                            qua_version = qua_versions[i] if i < len(qua_versions) else "N/A"
                            self.anomaly_detector.record_anomaly('both', 'ad_click', group_name, f'{date}',
                                               anomaly_display_value, f'比对照组低{click_diff:.2f}%',
                                               qua_version=qua_version, specific_date=date)

                        # 比较下载点击率（阈值：10%）
                        control_download_ratio = control_day_data['download_ratio']
                        download_display_value = float(f"{download_ratio:.2f}")  # 保留2位小数精度
                        control_download_display_value = float(f"{control_download_ratio:.2f}")  # 保留2位小数精度
                        download_diff = control_download_display_value - download_display_value  # 对照组 - 实验组
                        if download_diff >= 10.0:  # 10%，基于显示精度比较
                            # 创建用于表格显示的HTML格式
                            download_ratio_formatted = f'<span style="color: red;">{download_ratio_formatted}</span>'

                            # 创建用于异常记录的纯文本格式（不包含HTML标签）
                            anomaly_display_value = f'{download_ratio:.2f}%'

                            # 记录异常数据
                            group_name = f"实验组{i+1}" if i < 3 else "对照组"
                            qua_version = qua_versions[i] if i < len(qua_versions) else "N/A"
                            self.anomaly_detector.record_anomaly('both', 'ad_download', group_name, f'{date}',
                                               anomaly_display_value, f'比对照组低{download_diff:.2f}%',
                                               qua_version=qua_version, specific_date=date)

                        # 比较安装下载率（阈值：10%）
                        control_install_ratio = control_day_data['install_ratio']
                        install_display_value = float(f"{install_ratio:.2f}")  # 保留2位小数精度
                        control_install_display_value = float(f"{control_install_ratio:.2f}")  # 保留2位小数精度
                        install_diff = control_install_display_value - install_display_value  # 对照组 - 实验组
                        if install_diff >= 10.0:  # 10%，基于显示精度比较
                            # 创建用于表格显示的HTML格式
                            install_ratio_formatted = f'<span style="color: red;">{install_ratio_formatted}</span>'

                            # 创建用于异常记录的纯文本格式（不包含HTML标签）
                            anomaly_display_value = f'{install_ratio:.2f}%'

                            # 记录异常数据
                            group_name = f"实验组{i+1}" if i < 3 else "对照组"
                            qua_version = qua_versions[i] if i < len(qua_versions) else "N/A"
                            self.anomaly_detector.record_anomaly('both', 'ad_install', group_name, f'{date}',
                                               anomaly_display_value, f'比对照组低{install_diff:.2f}%',
                                               qua_version=qua_version, specific_date=date)

                # 添加到结果中
                result['ad_exposure'][i].append(exposure)
                result['ad_click'][i].append(click)
                result['ad_download'][i].append(download)
                result['ad_install'][i].append(install)
                result['click_ratio'][i].append(click_ratio_formatted)
                result['download_ratio'][i].append(download_ratio_formatted)
                result['install_ratio'][i].append(install_ratio_formatted)

    def _process_net_data(self, net_data: Dict[str, Any], qua_versions: List[str], result: Dict[str, List[List[str]]], expected_dates: List[str]) -> None:
        """处理联网数据 - 使用统一排序策略"""
        # 验证数据对齐
        self._validate_data_alignment(net_data, expected_dates, qua_versions, "联网")

        # 统一使用升序排序（修复原来的降序问题）
        for date in sorted(net_data.keys()):
            for i, qua_version in enumerate(qua_versions):
                if qua_version == "/" or qua_version not in net_data[date]:
                    result['net_users'][i].append("/")
                else:
                    user_count = net_data[date][qua_version]
                    result['net_users'][i].append(user_count)

    def _process_popup_data(self, popup_data: Dict[str, Any], qua_versions: List[str], result: Dict[str, List[List[str]]], expected_dates: List[str]) -> None:
        """处理弹窗数据 - 支持实验组与对照组比较"""
        # 验证数据对齐
        self._validate_data_alignment(popup_data, expected_dates, qua_versions, "弹窗")

        # 第一轮：收集所有原始数据
        raw_popup_data = []
        for i, qua_version in enumerate(qua_versions):
            qua_raw_data = []
            for date in sorted(popup_data.keys()):
                if qua_version == "/" or qua_version not in popup_data[date]:
                    qua_raw_data.append(None)
                else:
                    qua_data = popup_data[date][qua_version]
                    success_rate = qua_data.get('弹窗成功率(%)', 0)
                    qua_raw_data.append(success_rate)
            raw_popup_data.append(qua_raw_data)

        # 第二轮：进行实验组与对照组比较，生成最终格式化数据
        self._process_popup_comparison(raw_popup_data, qua_versions, result, sorted(popup_data.keys()))

    def _process_popup_comparison(self, raw_popup_data: List[List], qua_versions: List[str], result: Dict[str, List[List[str]]], sorted_dates: List[str]) -> None:
        """处理弹窗数据，进行实验组与对照组比较"""
        # 确定对照组索引（通常是最后一个非占位符版本）
        control_group_index = None
        for i in range(len(qua_versions) - 1, -1, -1):
            if qua_versions[i] != "/" and raw_popup_data[i] is not None:
                control_group_index = i
                break

        if control_group_index is None:
            # 如果没有对照组，直接格式化所有数据
            for i, qua_data in enumerate(raw_popup_data):
                for day_idx, success_rate in enumerate(qua_data):
                    if success_rate is None:
                        result['popup_rate'][i].append("/")
                    else:
                        result['popup_rate'][i].append(f"{success_rate:.2f}%")
            return

        control_group_popup = raw_popup_data[control_group_index]

        # 处理每个组的弹窗数据
        for i, qua_data in enumerate(raw_popup_data):
            group_name = f"实验组{i+1}" if i < 3 else "对照组"

            if qua_data is None or all(day is None for day in qua_data):
                # 占位符组
                for _ in range(len(sorted_dates)):
                    result['popup_rate'][i].append("/")
                continue

            # 处理每天的数据
            for day_idx, (date, success_rate) in enumerate(zip(sorted_dates, qua_data)):
                if success_rate is None:
                    result['popup_rate'][i].append("/")
                    continue

                formatted_rate = f"{success_rate:.2f}%"

                # 如果是实验组，与对照组比较
                if i != control_group_index and i < 3:  # 实验组（前3组）
                    if (day_idx < len(control_group_popup) and
                        control_group_popup[day_idx] is not None):
                        control_success_rate = control_group_popup[day_idx]

                        # 基于显示格式的数值进行比较，确保与用户看到的数据一致
                        # 比较弹窗成功率（对照组 - 实验组 >= 1%表示实验组表现差）
                        success_rate_display_value = float(f"{success_rate:.2f}")  # 保留2位小数精度
                        control_success_rate_display_value = float(f"{control_success_rate:.2f}")  # 保留2位小数精度
                        rate_diff = control_success_rate_display_value - success_rate_display_value
                        if rate_diff >= 1.0:  # 1%，基于显示精度比较
                            # 创建用于表格显示的HTML格式
                            formatted_rate = f'<span style="color: red;">{formatted_rate}</span>'

                            # 创建用于异常记录的纯文本格式（不包含HTML标签）
                            anomaly_display_value = f'{success_rate:.2f}%'

                            # 记录异常数据
                            group_name = f"实验组{i+1}" if i < 3 else "对照组"
                            qua_version = qua_versions[i] if i < len(qua_versions) else "N/A"
                            self.anomaly_detector.record_anomaly('both', 'popup', group_name, f'第{day_idx+1}天',
                                               anomaly_display_value, f'比对照组低{rate_diff:.2f}%',
                                               qua_version=qua_version, specific_date=date)

                result['popup_rate'][i].append(formatted_rate)

    def _process_cloud_game_data(self, plugin_data: Dict[str, Any], rqd_versions: List[str], result: Dict[str, List[List[str]]], expected_dates: List[str]) -> None:
        """处理云游戏数据 - 支持阈值检测"""
        # 验证数据对齐
        self._validate_data_alignment(plugin_data, expected_dates, rqd_versions, "云游戏")

        # 统一使用升序排序
        sorted_dates = sorted(plugin_data.keys())
        for day_idx, date in enumerate(sorted_dates):
            for i, rqd_version in enumerate(rqd_versions):
                if rqd_version == "/" or rqd_version not in plugin_data[date]:
                    result['cloud_game_rate'][i].append("/")
                else:
                    app_data = plugin_data[date][rqd_version]
                    success_rate = app_data.get('云游插件拉起成功率', 0)

                    # 转换为百分比（API返回的是小数，如0.98表示98%）
                    success_rate_percent = success_rate * 100
                    formatted_rate = f"{success_rate_percent:.2f}%"

                    # 检查是否低于97%阈值
                    if success_rate_percent <= 97.0:
                        # 创建用于表格显示的HTML格式
                        formatted_rate = f'<span style="color: red;">{formatted_rate}</span>'

                        # 创建用于异常记录的纯文本格式（不包含HTML标签）
                        anomaly_display_value = f'{success_rate_percent:.2f}%'

                        # 记录异常数据
                        group_name = f"实验组{i+1}" if i < 3 else "对照组"
                        self.anomaly_detector.record_anomaly('both', 'cloud_game', group_name, f'第{day_idx+1}天',
                                           anomaly_display_value, f'低于97%阈值({success_rate_percent:.2f}%)',
                                           qua_version=rqd_version, specific_date=date)

                    result['cloud_game_rate'][i].append(formatted_rate)


def collect_gray_data(config: Dict[str, Any] = None) -> tuple:
    """收集灰度数据 - 主要接口

    Returns:
        tuple: (GrayDataParams, str) - 灰度数据参数和异常数据汇总
    """
    if config is None:
        # 默认配置
        config = {
            # 新用户版本配置
            'new_user': {
                'qua_versions': ["TMAF_899_P_8547", "TMAF_899_P_8548", "/", "TMAF_899_P_8550"],
                'rqd_versions': ["8.9.9_8994130_8547", "8.9.9_8994130_8548", "/", "8.9.9_8994130_8550"],
                'test_dates': ["2025/06/06", "2025/06/07", "2025/06/08"]
            },
            # 老用户版本配置
            'old_user': {
                'qua_versions': ["TMAF_899_P_8549", "TMAF_899_P_8551", "/", "TMAF_899_P_8552"],
                'rqd_versions': ["8.9.9_8994130_8549", "8.9.9_8994130_8551", "/", "8.9.9_8994130_8552"],
                'test_dates': ["2025/06/06", "2025/06/07", "2025/06/08"]
            }
        }

    # 创建数据收集器并执行收集
    collector = GrayDataCollector()
    gray_data = collector.collect_gray_data(config)
    # gray_data = collector.collect_gray_data_test_crash_anr_only(config)
    anomaly_summary = collector.get_anomaly_summary()
    ai_anomaly_summary = collector.get_ai_anomaly_summary(anomaly_summary)
    print(f'ai_anomaly_summary = ', ai_anomaly_summary)

    return gray_data, anomaly_summary, ai_anomaly_summary

def auto_collect_gray_data(version, is_fetch_data = "1"):
    """从iwiki文档解析配置，自动收集数据并追加到iwiki文档"""
    print("\n" + "=" * 60)
    print("从iwiki文档解析配置，自动收集数据并追加到iwiki文档")
    print("=" * 60)

    api = create_gray_data_api()

    # 从iWiki文档解析配置
    # version = "900"
    iwiki_url, custom_config = api.get_config_from_iwiki(version)
    print(f"\n从iWiki文档解析配置:\n{iwiki_url}")

    # 是否需要从数据源拉取数据。0表示不拉取，1表示拉取
    if is_fetch_data == "0":
        return iwiki_url

    # 收集数据
    gray_data, anomaly_summary, ai_anomaly_summary = collect_gray_data(custom_config)

    # 生成报告
    # 追加到iWiki文档
    # docid = 4015240554
    docid = 4015180169
    # title = "900-0615"
    title = f"{version}灰度实验分析"
    print("\n生成报告内容...")
    api.append_to_iwiki(
        iwiki_url=iwiki_url,
        title=title,
        params=gray_data,
        anomaly_summary=anomaly_summary,
        ai_anomaly_summary=ai_anomaly_summary
    )
    print(f"访问链接: {iwiki_url}")

    return iwiki_url
