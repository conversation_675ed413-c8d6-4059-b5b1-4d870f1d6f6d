#!/usr/bin/env python3
import sys
import os
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from common.client.iwiki_client import IWikiClient
from common.tools.text_parse_utils import TextParseUtils
from .gray_config_parser import GrayConfigParser
from .templates.template_new_users import GRAY_DATA_NEW_USERS
from .templates.template_old_users import GRAY_DATA_OLD_USERS


@dataclass
class GrayDataParams:
    """灰度数据参数类"""
    
    # QUA版本信息 - 新用户
    qua_exp1: str = "TMAF_899_P_7921"      # 新用户实验组1 QUA
    qua_exp2: str = "TMAF_899_P_7922"      # 新用户实验组2 QUA
    qua_exp3: str = "TMAF_899_P_7923"      # 新用户实验组3 QUA
    qua_control: str = "TMAF_899_P_7920"   # 新用户对照组 QUA

    # QUA版本信息 - 老用户（套壳9）
    qua_old_exp1: str = "TMAF_899_P_8921"      # 老用户实验组1 QUA
    qua_old_exp2: str = "TMAF_899_P_8922"      # 老用户实验组2 QUA
    qua_old_exp3: str = "TMAF_899_P_8923"      # 老用户实验组3 QUA
    qua_old_control: str = "TMAF_899_P_8920"   # 老用户对照组 QUA
    
    # 测试日期（4天）
    test_dates: List[str] = None          # 新用户测试日期
    test_dates_old: List[str] = None      # 老用户测试日期（可与新用户不同）
    
    # 新用户启动速度数据
    new_regular_hot: List[str] = None      # 常规热启动 [exp1, exp2, exp3, control]
    new_regular_cold: List[str] = None     # 常规冷启动
    new_outcall_hot: List[str] = None      # 外call热启动
    new_outcall_cold: List[str] = None     # 外call冷启动
    
    # 老用户启动速度数据
    old_regular_hot: List[str] = None      # 常规热启动 [exp1, exp2, exp3, control]
    old_regular_cold: List[str] = None     # 常规冷启动
    old_outcall_hot: List[str] = None      # 外call热启动
    old_outcall_cold: List[str] = None     # 外call冷启动
    
    # 新用户Crash率数据 (4组 x 4天)
    new_device_crash: List[List[str]] = None      # [[exp1_4天], [exp2_4天], [exp3_4天], [control_4天]]
    new_avg_crash: List[List[str]] = None         # 平均crash率
    new_fg_crash: List[List[str]] = None          # 前台crash率
    new_anr_rate: List[List[str]] = None          # ANR率
    
    # 老用户Crash率数据 (4组 x 4天)
    old_device_crash: List[List[str]] = None      # [[exp1_4天], [exp2_4天], [exp3_4天], [control_4天]]
    old_avg_crash: List[List[str]] = None         # 平均crash率
    old_fg_crash: List[List[str]] = None          # 前台crash率
    old_anr_rate: List[List[str]] = None          # ANR率
    
    # 新用户下载数据 (4组 x 4天)
    new_download_start: List[List[str]] = None    # 外call开始下载率
    new_download_success: List[List[str]] = None  # 外call成功下载率
    new_download_cvr: List[List[str]] = None      # 下载安装CVR
    
    # 老用户下载数据 (4组 x 4天)
    old_download_start: List[List[str]] = None    # 外call开始下载率
    old_download_success: List[List[str]] = None  # 外call成功下载率
    old_download_cvr: List[List[str]] = None      # 下载安装CVR
    
    # 新用户广告数据 (4组 x 4天)
    new_ad_exposure: List[List[str]] = None       # 广告曝光
    new_ad_click: List[List[str]] = None          # 广告点击
    new_ad_download: List[List[str]] = None       # 广告下载
    new_ad_install: List[List[str]] = None        # 广告安装
    new_click_ratio: List[List[str]] = None       # 点击/曝光比率
    new_download_ratio: List[List[str]] = None    # 下载/点击比率
    new_install_ratio: List[List[str]] = None     # 安装/下载比率
    
    # 老用户广告数据 (4组 x 4天)
    old_ad_exposure: List[List[str]] = None       # 广告曝光
    old_ad_click: List[List[str]] = None          # 广告点击
    old_ad_download: List[List[str]] = None       # 广告下载
    old_ad_install: List[List[str]] = None        # 广告安装
    old_click_ratio: List[List[str]] = None       # 点击/曝光比率
    old_download_ratio: List[List[str]] = None    # 下载/点击比率
    old_install_ratio: List[List[str]] = None     # 安装/下载比率
    
    # 新用户网络数据 (4组 x 4天)
    new_net_users: List[List[str]] = None         # 联网用户数
    new_popup_rate: List[List[str]] = None        # 弹窗成功率
    new_cloud_game_rate: List[List[str]] = None   # 云游插件成功率
    
    # 老用户网络数据 (4组 x 4天)
    old_net_users: List[List[str]] = None         # 联网用户数
    old_popup_rate: List[List[str]] = None        # 弹窗成功率
    old_cloud_game_rate: List[List[str]] = None   # 云游插件成功率
    
    def __post_init__(self):
        """初始化默认值"""
        if self.test_dates is None:
            self.test_dates = ["2025/5/30", "2025/5/31", "2025/6/1", "2025/6/2"]

        # 如果老用户测试日期未设置，则使用新用户的测试日期
        if self.test_dates_old is None:
            self.test_dates_old = self.test_dates.copy()

        # 初始化所有None的列表字段为默认值
        self._init_default_values()

        # 验证和修复数据结构
        self._validate_and_fix_data()
    
    def _init_default_values(self):
        """初始化默认值"""
        # 启动速度默认值
        if self.new_regular_hot is None:
            self.new_regular_hot = ["/", "/", "/", "/"]
        if self.new_regular_cold is None:
            self.new_regular_cold = ["/", "/", "/", "/"]
        if self.new_outcall_hot is None:
            self.new_outcall_hot = ["/", "/", "/", "/"]
        if self.new_outcall_cold is None:
            self.new_outcall_cold = ["/", "/", "/", "/"]

        if self.old_regular_hot is None:
            self.old_regular_hot = ["/", "/", "/", "/"]
        if self.old_regular_cold is None:
            self.old_regular_cold = ["/", "/", "/", "/"]
        if self.old_outcall_hot is None:
            self.old_outcall_hot = ["/", "/", "/", "/"]
        if self.old_outcall_cold is None:
            self.old_outcall_cold = ["/", "/", "/", "/"]
        
        # 初始化4组x4天的数据
        self._init_matrix_data()
    
    def _init_matrix_data(self):
        """初始化矩阵数据（4组x4天）"""
        # Crash率数据
        if self.new_device_crash is None:
            self.new_device_crash = [
                ["/", "/", "/", "/"],  # exp1
                ["/", "/", "/", "/"],  # exp2
                ["/", "/", "/", "/"],  # exp3
                ["/", "0.16%", "0.18%", "0.17%"]   # control
            ]
        
        if self.new_avg_crash is None:
            self.new_avg_crash = [
                ["/", "/", "/", "/"],
                ["/", "/", "/", "/"],
                ["/", "/", "/", "/"],
                ["/", "/", "/", "/"]
            ]

        if self.new_fg_crash is None:
            self.new_fg_crash = [
                ["/", "/", "/", "/"],
                ["/", "/", "/", "/"],
                ["/", "/", "/", "/"],
                ["/", "/", "/", "/"]
            ]

        if self.new_anr_rate is None:
            self.new_anr_rate = [
                ["/", "/", "/", "/"],
                ["/", "/", "/", "/"],
                ["/", "/", "/", "/"],
                ["/", "/", "/", "/"]
            ]

        # 老用户Crash率数据
        if self.old_device_crash is None:
            self.old_device_crash = [
                ["/", "/", "/", "/"],
                ["/", "/", "/", "/"],
                ["/", "/", "/", "/"],
                ["/", "/", "/", "/"]
            ]
        
        # 其他数据类似初始化...
        self._init_remaining_matrix_data()
    
    def _init_remaining_matrix_data(self):
        """初始化剩余的矩阵数据"""
        # 这里可以继续初始化其他矩阵数据
        # 为了简化，使用默认值
        default_4x4 = [
            ["/", "/", "/", "/"],
            ["/", "/", "/", "/"],
            ["/", "/", "/", "/"],
            ["/", "/", "/", "/"]
        ]
        
        # 如果字段为None，则使用默认值
        for field_name in ['old_avg_crash', 'old_fg_crash', 'old_anr_rate',
                          'new_download_start', 'new_download_success', 'new_download_cvr',
                          'old_download_start', 'old_download_success', 'old_download_cvr',
                          'new_ad_exposure', 'new_ad_click', 'new_ad_download', 'new_ad_install',
                          'new_click_ratio', 'new_download_ratio', 'new_install_ratio',
                          'old_ad_exposure', 'old_ad_click', 'old_ad_download', 'old_ad_install',
                          'old_click_ratio', 'old_download_ratio', 'old_install_ratio',
                          'new_net_users', 'new_popup_rate', 'new_cloud_game_rate',
                          'old_net_users', 'old_popup_rate', 'old_cloud_game_rate']:
            if getattr(self, field_name) is None:
                setattr(self, field_name, [row[:] for row in default_4x4])  # 深拷贝

    def _validate_and_fix_data(self):
        """验证和修复数据结构，确保兼容性"""
        # 确保测试日期至少有4个
        if len(self.test_dates) < 4:
            # 补齐到4个日期
            last_date = self.test_dates[-1] if self.test_dates else "2025/5/30"
            while len(self.test_dates) < 4:
                # 递增日期
                parts = last_date.split('/')
                day = int(parts[2]) + 1
                self.test_dates.append(f"{parts[0]}/{parts[1]}/{day}")
                last_date = self.test_dates[-1]

        if len(self.test_dates_old) < 4:
            # 补齐老用户测试日期
            last_date = self.test_dates_old[-1] if self.test_dates_old else "2025/5/30"
            while len(self.test_dates_old) < 4:
                parts = last_date.split('/')
                day = int(parts[2]) + 1
                self.test_dates_old.append(f"{parts[0]}/{parts[1]}/{day}")
                last_date = self.test_dates_old[-1]

        # 验证和修复矩阵数据结构（4组x4天）
        matrix_fields = [
            'new_device_crash', 'new_avg_crash', 'new_fg_crash', 'new_anr_rate',
            'old_device_crash', 'old_avg_crash', 'old_fg_crash', 'old_anr_rate',
            'new_download_start', 'new_download_success', 'new_download_cvr',
            'old_download_start', 'old_download_success', 'old_download_cvr',
            'new_ad_exposure', 'new_ad_click', 'new_ad_download', 'new_ad_install',
            'new_click_ratio', 'new_download_ratio', 'new_install_ratio',
            'old_ad_exposure', 'old_ad_click', 'old_ad_download', 'old_ad_install',
            'old_click_ratio', 'old_download_ratio', 'old_install_ratio',
            'new_net_users', 'new_popup_rate', 'new_cloud_game_rate',
            'old_net_users', 'old_popup_rate', 'old_cloud_game_rate'
        ]

        for field_name in matrix_fields:
            field_value = getattr(self, field_name)
            if field_value is not None:
                # 确保是4组数据
                while len(field_value) < 4:
                    field_value.append(["/"] * 4)

                # 确保每组有4天数据
                for i in range(len(field_value)):
                    if not isinstance(field_value[i], list):
                        field_value[i] = ["/"] * 4
                    while len(field_value[i]) < 4:
                        field_value[i].append("/")

                setattr(self, field_name, field_value)


class GrayDataAPI:
    """灰度数据API"""
    
    def __init__(self, iwiki_client: Optional[IWikiClient] = None):
        self.iwiki_client = iwiki_client or IWikiClient()
    
    def convert_params_to_template_data(self, params: GrayDataParams) -> Dict[str, str]:
        """将参数对象转换为模板数据字典"""
        data = {}
        
        # QUA版本 - 新用户和老用户使用不同的QUA
        data.update({
            # 新用户QUA版本
            'qua_1': params.qua_exp1,
            'qua_2': params.qua_exp2,
            'qua_3': params.qua_exp3,
            'qua_control': params.qua_control,
            # 老用户QUA版本（完全不同）
            'qua_old_1': params.qua_old_exp1,
            'qua_old_2': params.qua_old_exp2,
            'qua_old_3': params.qua_old_exp3,
            'qua_old_control': params.qua_old_control,
        })
        
        # 启动速度数据
        groups = ['1', '2', '3', 'control']
        for i, group in enumerate(groups):
            data[f'regular_hot_{group}'] = params.new_regular_hot[i]
            data[f'regular_cold_{group}'] = params.new_regular_cold[i]
            data[f'regular_outcall_hot_{group}'] = params.new_outcall_hot[i]
            data[f'regular_outcall_cold_{group}'] = params.new_outcall_cold[i]
            
            data[f'regular_hot_old_{group}'] = params.old_regular_hot[i]
            data[f'regular_cold_old_{group}'] = params.old_regular_cold[i]
            data[f'regular_outcall_hot_old_{group}'] = params.old_outcall_hot[i]
            data[f'regular_outcall_cold_old_{group}'] = params.old_outcall_cold[i]
        
        # Crash率数据（4组x4天）
        days = ['day1', 'day2', 'day3', 'day4']
        for i, group in enumerate(groups):
            for j, day in enumerate(days):
                # 安全获取日期，如果索引超出范围则使用默认值
                new_date = params.test_dates[j] if j < len(params.test_dates) else f"2025/5/{30+j}"
                old_date = params.test_dates_old[j] if j < len(params.test_dates_old) else f"2025/5/{30+j}"

                # 新用户
                data[f'device_crash_{group}_{day}'] = params.new_device_crash[i][j]
                data[f'avg_device_crash_{group}_{day}'] = params.new_avg_crash[i][j]
                data[f'fg_device_crash_{group}_{day}'] = params.new_fg_crash[i][j]
                data[f'anr_rate_{group}_{day}'] = params.new_anr_rate[i][j]
                data[f'date_{group}_{day}'] = new_date

                # 老用户 - 使用独立的测试日期
                data[f'device_crash_old_{group}_{day}'] = params.old_device_crash[i][j]
                data[f'avg_device_crash_old_{group}_{day}'] = params.old_avg_crash[i][j]
                data[f'fg_device_crash_old_{group}_{day}'] = params.old_fg_crash[i][j]
                data[f'anr_rate_old_{group}_{day}'] = params.old_anr_rate[i][j]
                data[f'date_old_{group}_{day}'] = old_date
        
        # 其他数据类似处理...
        self._add_remaining_data(data, params, groups, days)
        
        return data
    
    def _add_remaining_data(self, data: Dict[str, str], params: GrayDataParams, groups: List[str], days: List[str]):
        """添加剩余数据到字典"""

        def safe_get_matrix_value(matrix, i, j, default="/"):
            """安全获取矩阵数据，如果索引超出范围则使用默认值"""
            try:
                if matrix and i < len(matrix) and j < len(matrix[i]):
                    return matrix[i][j]
                return default
            except (IndexError, TypeError):
                return default

        for i, group in enumerate(groups):
            for j, day in enumerate(days):
                # 下载数据
                data[f'outcall_start_download_{group}_{day}'] = safe_get_matrix_value(params.new_download_start, i, j)
                data[f'outcall_success_download_{group}_{day}'] = safe_get_matrix_value(params.new_download_success, i, j)
                data[f'download_install_cvr_{group}_{day}'] = safe_get_matrix_value(params.new_download_cvr, i, j)

                data[f'outcall_start_download_old_{group}_{day}'] = safe_get_matrix_value(params.old_download_start, i, j)
                data[f'outcall_success_download_old_{group}_{day}'] = safe_get_matrix_value(params.old_download_success, i, j)
                data[f'download_install_cvr_old_{group}_{day}'] = safe_get_matrix_value(params.old_download_cvr, i, j)
                
                # 广告数据
                data[f'ad_exposure_{group}_{day}'] = safe_get_matrix_value(params.new_ad_exposure, i, j)
                data[f'ad_click_{group}_{day}'] = safe_get_matrix_value(params.new_ad_click, i, j)
                data[f'ad_download_{group}_{day}'] = safe_get_matrix_value(params.new_ad_download, i, j)
                data[f'ad_install_{group}_{day}'] = safe_get_matrix_value(params.new_ad_install, i, j)
                data[f'click_exposure_ratio_{group}_{day}'] = safe_get_matrix_value(params.new_click_ratio, i, j)
                data[f'download_click_ratio_{group}_{day}'] = safe_get_matrix_value(params.new_download_ratio, i, j)
                data[f'install_download_ratio_{group}_{day}'] = safe_get_matrix_value(params.new_install_ratio, i, j)

                data[f'ad_exposure_old_{group}_{day}'] = safe_get_matrix_value(params.old_ad_exposure, i, j)
                data[f'ad_click_old_{group}_{day}'] = safe_get_matrix_value(params.old_ad_click, i, j)
                data[f'ad_download_old_{group}_{day}'] = safe_get_matrix_value(params.old_ad_download, i, j)
                data[f'ad_install_old_{group}_{day}'] = safe_get_matrix_value(params.old_ad_install, i, j)
                data[f'click_exposure_ratio_old_{group}_{day}'] = safe_get_matrix_value(params.old_click_ratio, i, j)
                data[f'download_click_ratio_old_{group}_{day}'] = safe_get_matrix_value(params.old_download_ratio, i, j)
                data[f'install_download_ratio_old_{group}_{day}'] = safe_get_matrix_value(params.old_install_ratio, i, j)

                # 网络数据
                data[f'net_user_count_{group}_{day}'] = safe_get_matrix_value(params.new_net_users, i, j)
                data[f'popup_success_rate_{group}_{day}'] = safe_get_matrix_value(params.new_popup_rate, i, j)
                data[f'cloud_game_plugin_launch_success_rate_{group}_{day}'] = safe_get_matrix_value(params.new_cloud_game_rate, i, j)

                data[f'net_user_count_old_{group}_{day}'] = safe_get_matrix_value(params.old_net_users, i, j)
                data[f'popup_success_rate_old_{group}_{day}'] = safe_get_matrix_value(params.old_popup_rate, i, j)
                data[f'cloud_game_plugin_launch_success_rate_old_{group}_{day}'] = safe_get_matrix_value(params.old_cloud_game_rate, i, j)
    
    def _verify_template_mapping(self, params: GrayDataParams, template_data: Dict[str, str]) -> bool:
        """验证模板数据映射的正确性"""
        print("🔍 验证模板数据映射...")

        try:
            # 验证QUA版本映射
            assert template_data['qua_1'] == params.qua_exp1, f"QUA映射错误: qua_1={template_data['qua_1']} != {params.qua_exp1}"
            assert template_data['qua_2'] == params.qua_exp2, f"QUA映射错误: qua_2={template_data['qua_2']} != {params.qua_exp2}"
            assert template_data['qua_3'] == params.qua_exp3, f"QUA映射错误: qua_3={template_data['qua_3']} != {params.qua_exp3}"
            assert template_data['qua_control'] == params.qua_control, f"QUA映射错误: qua_control={template_data['qua_control']} != {params.qua_control}"

            # 验证老用户QUA版本映射
            assert template_data['qua_old_1'] == params.qua_old_exp1, f"老用户QUA映射错误: qua_old_1={template_data['qua_old_1']} != {params.qua_old_exp1}"
            assert template_data['qua_old_2'] == params.qua_old_exp2, f"老用户QUA映射错误: qua_old_2={template_data['qua_old_2']} != {params.qua_old_exp2}"
            assert template_data['qua_old_3'] == params.qua_old_exp3, f"老用户QUA映射错误: qua_old_3={template_data['qua_old_3']} != {params.qua_old_exp3}"
            assert template_data['qua_old_control'] == params.qua_old_control, f"老用户QUA映射错误: qua_old_control={template_data['qua_old_control']} != {params.qua_old_control}"

            # 验证关键数据点映射（抽样验证）
            # 验证实验组1第1天的crash数据
            expected_crash_1_1 = params.new_device_crash[0][0]
            actual_crash_1_1 = template_data['device_crash_1_day1']
            assert actual_crash_1_1 == expected_crash_1_1, f"Crash数据映射错误: device_crash_1_day1={actual_crash_1_1} != {expected_crash_1_1}"

            # 验证实验组2第2天的crash数据
            expected_crash_2_2 = params.new_device_crash[1][1]
            actual_crash_2_2 = template_data['device_crash_2_day2']
            assert actual_crash_2_2 == expected_crash_2_2, f"Crash数据映射错误: device_crash_2_day2={actual_crash_2_2} != {expected_crash_2_2}"

            # 验证对照组第4天的crash数据
            expected_crash_control_4 = params.new_device_crash[3][3]
            actual_crash_control_4 = template_data['device_crash_control_day4']
            assert actual_crash_control_4 == expected_crash_control_4, f"Crash数据映射错误: device_crash_control_day4={actual_crash_control_4} != {expected_crash_control_4}"

            # 验证启动速度数据映射
            expected_hot_1 = params.new_regular_hot[0]
            actual_hot_1 = template_data['regular_hot_1']
            assert actual_hot_1 == expected_hot_1, f"启动速度映射错误: regular_hot_1={actual_hot_1} != {expected_hot_1}"

            # 验证老用户数据映射
            expected_old_crash_1_1 = params.old_device_crash[0][0]
            actual_old_crash_1_1 = template_data['device_crash_old_1_day1']
            assert actual_old_crash_1_1 == expected_old_crash_1_1, f"老用户Crash数据映射错误: device_crash_old_1_day1={actual_old_crash_1_1} != {expected_old_crash_1_1}"

            print("模板数据映射验证通过")
            return True

        except AssertionError as e:
            print(f"模板数据映射验证失败: {e}")
            return False
        except Exception as e:
            print(f"模板数据映射验证异常: {e}")
            return False

    def fill_template(self, params: GrayDataParams, template_type: str = "both", anomaly_summary: str = None, ai_anomaly_summary: str = None) -> str:
        """填充模板 - 包含数据映射验证和异常数据"""

        # 添加异常数据部分
        content = ""
        if anomaly_summary and anomaly_summary.strip() != "未发现异常数据":
            content = f"\n\n---\n\n# AI总结分析\n\n{ai_anomaly_summary}"
            content += f"\n\n---\n\n# 异常数据\n\n{anomaly_summary}"
        elif anomaly_summary:
            content = f"\n\n---\n\n# AI总结分析\n\n{ai_anomaly_summary}"
            content += f"\n\n---\n\n# 异常数据\n\n{anomaly_summary}"
        
        data = self.convert_params_to_template_data(params)

        # 验证模板数据映射的正确性
        if not self._verify_template_mapping(params, data):
            print("模板数据映射验证失败，但继续生成模板")

        if template_type == "new_users":
            content += GRAY_DATA_NEW_USERS.format(**data)
        elif template_type == "old_users":
            content += GRAY_DATA_OLD_USERS.format(**data)
        elif template_type == "both":
            new_content = GRAY_DATA_NEW_USERS.format(**data)
            old_content = GRAY_DATA_OLD_USERS.format(**data)
            content += f"{new_content}\n\n---\n\n{old_content}"
        else:
            raise ValueError(f"不支持的模板类型: {template_type}")

        return content

    def get_config_from_iwiki(self, version: str) -> tuple[str, Dict[str, Any]]:
        """从iWiki文档解析 灰度实验配置"""
        # 获取 灰度实验分析 映射表
        mapping_info = self.iwiki_client.get_doc_body("https://iwiki.woa.com/p/4015250185")
        if not mapping_info:
            raise ValueError("获取灰度实验分析映射表失败")
        mapping_info = TextParseUtils.extract_code_block(mapping_info)
        json_items = TextParseUtils.parse_json_lines(mapping_info)
        # 获取对应版本的iwiki链接
        iwiki_url = ""
        for item in json_items:
            if item.get('version') == version:  
                iwiki_url = item.get('iwiki_url')
        if not iwiki_url:
            raise ValueError(f"未找到版本号为 {version} 的iwiki链接")
        print(f"获取到的iwiki链接为: {iwiki_url}")

        # 从iwiki链接中解析配置
        body_content = self.iwiki_client.get_doc_body(iwiki_url)
        custom_config = GrayConfigParser.parse_gray_config_from_markdown(body_content)
        return iwiki_url, custom_config

    
    def append_to_iwiki(self, iwiki_url: str, title: str, params: GrayDataParams, prefix: str = "", suffix: str = "", anomaly_summary: str = None, ai_anomaly_summary: str = None) -> Dict[str, Any]:
        """追加到iwiki文档"""
        try:
            content = self.fill_template(params, "both", anomaly_summary, ai_anomaly_summary)

            if prefix or suffix:
                content = f"{prefix}{content}{suffix}"

            result = self.iwiki_client.append_document(iwiki_url, title, content)
            print(f"result: {result}")
            
            if result.get('code') == 'Ok':
                print("✓ 灰度数据追加成功")
                return {
                    'success': True,
                    'message': '灰度数据追加成功',
                    'content_length': len(content),
                    'result': result
                }
            else:
                return {
                    'success': False,
                    'error': f'追加失败: {result}',
                    'result': result
                }
        except Exception as e:
            return {
                'success': False,
                'error': f'操作异常: {str(e)}'
            }
    
    def create_and_append(self, title: str, params: GrayDataParams, prefix: str = "", suffix: str = "", anomaly_summary: str = None) -> Dict[str, Any]:
        """创建文档并追加"""
        try:
            # 创建文档
            create_result = self.iwiki_client.create_document(title)

            if create_result.get('code') != 'Ok':
                return {
                    'success': False,
                    'error': f'文档创建失败: {create_result}',
                    'result': create_result
                }

            docid = create_result.get('data', {}).get('docid')
            if not docid:
                return {
                    'success': False,
                    'error': '文档创建成功但未返回docid',
                    'result': create_result
                }

            # 追加内容
            append_result = self.append_to_iwiki(docid, title, params, prefix, suffix, anomaly_summary)
            
            if append_result['success']:
                append_result['docid'] = docid
                append_result['create_result'] = create_result
            
            return append_result
            
        except Exception as e:
            return {
                'success': False,
                'error': f'操作异常: {str(e)}'
            }


def create_gray_data_api(iwiki_client: Optional[IWikiClient] = None) -> GrayDataAPI:
    """创建API实例"""
    return GrayDataAPI(iwiki_client)


def quick_append_with_params(docid: str, params: GrayDataParams, iwiki_client: Optional[IWikiClient] = None, anomaly_summary: str = None) -> Dict[str, Any]:
    """快速追加（使用参数对象）"""
    api = create_gray_data_api(iwiki_client)
    return api.append_to_iwiki(docid, "灰度数据报告", params, anomaly_summary=anomaly_summary)
