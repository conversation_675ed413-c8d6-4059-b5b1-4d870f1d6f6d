#!/usr/bin/env python3
"""
灰度数据收集器使用示例
包含各种使用场景的示例代码
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from version_tool.gray_data.gray_data_collector import GrayDataCollector, collect_gray_data
from version_tool.gray_data.gray_data_api import create_gray_data_api


def example_custom_config():
    """自定义配置示例"""
    print("\n" + "=" * 60)
    print("自定义配置示例")
    print("=" * 60)

    # 自定义配置
    custom_config = {
        'new_user': {
            'qua_versions': ["TMAF_899_P_9001", "TMAF_899_P_9002", "TMAF_899_P_9003", "TMAF_899_P_9000"],
            'rqd_versions': ["8.9.9_9001", "8.9.9_9002", "8.9.9_9003", "8.9.9_9000"],
            'test_dates': ["2025/06/15", "2025/06/16", "2025/06/17"]
        },
        'old_user': {
            'qua_versions': ["TMAF_899_P_9101", "TMAF_899_P_9102", "TMAF_899_P_9103", "TMAF_899_P_9100"],
            'rqd_versions': ["8.9.9_9101", "8.9.9_9102", "8.9.9_9103", "8.9.9_9100"],
            'test_dates': ["2025/06/18", "2025/06/19", "2025/06/20"]
        }
    }

    # 收集数据
    gray_data, anomaly_summary, ai_anomaly_summary = collect_gray_data(custom_config)

    print(f"自定义参数创建完成")
    print(f"  - 新用户QUA实验组1: {gray_data.qua_exp1}")
    print(f"  - 老用户QUA实验组1: {gray_data.qua_old_exp1}")
    print(f"  - 新用户测试日期: {gray_data.test_dates}")
    print(f"  - 老用户测试日期: {gray_data.test_dates_old}")

    return gray_data, anomaly_summary



def example_from_markdown():
    """从Markdown配置解析的示例"""
    print("\n" + "=" * 60)
    print("从Markdown配置解析示例")
    print("=" * 60)

    api = create_gray_data_api()

    # 从iWiki文档解析配置

    version = '班车0605'
    iwiki_url, custom_config = api.get_config_from_iwiki(version)
    print(f"获取到的iwiki链接为: {iwiki_url}")
    print(f"获取到的自定义配置为: {custom_config}")


    # 收集数据
    gray_data, anomaly_summary, ai_anomaly_summary = collect_gray_data(custom_config)

    # 生成报告
    # 追加到iWiki文档
    title = f'{version}灰度实验分析'
    print("\n生成报告内容...")
    api.append_to_iwiki(
        iwiki_url=iwiki_url,
        title=title,
        params=gray_data,
        anomaly_summary=anomaly_summary,
        ai_anomaly_summary=ai_anomaly_summary
    )
    print(f"访问链接: {iwiki_url}")

    return iwiki_url


def main():
    try:
        # example_custom_config()
        example_from_markdown()
    except Exception as e:
        print(f"\n执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
