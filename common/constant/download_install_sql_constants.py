"""
Download Install CVR SQL 常量定义文件
包含下载安装CVR查询的完整SQL语句模板
"""


class DownloadInstallSQLConstants:
    """下载安装CVR SQL 语句常量类"""
    
    # 下载安装CVR查询 SQL - 第一部分
    DOWNLOAD_INSTALL_CVR_SQL_PART1 = """
select
ds as 日期
, case when 'total' in ({select_qua_big_version}) then '合计' else qua_big_version end as 大版本号
, case when 'total' in ({select_qua}) then '合计' else qua end as qua
, case when 'total' in ({select_brand}) then '合计' else brand end as 品牌
, case when 'total' in ({select_appid}) then '合计' else cast(appid as string) end as appid
, case when 'total' in ({select_package_name}) then '合计' else package_name end as package_name
, case when 'total' in ({select_app_name}) then '合计' else app_name end as app_name
, case when 'total' in ({select_expand_guid}) then '合计' else guid end as guid
, case when 'total' in ({select_evil_level}) then '合计' when 'filter' in ({select_evil_level}) then '排除恶意' else evil_level end as 恶意等级
, case when 'total' in ({select_expand_download_id}) then '合计' else sim_download_id end as download_id
, case when 'total' in ({select_game_coop_type}) then '合计' else game_coop_type end as 精品联运
, case when 'total' in ({select_os_version}) then '合计' else os_version end as os_version
, case when 'total' in ({select_scene}) then '合计' else scene end as scene
, case when 'total' in ({select_scene_name}) then '合计' else scene_name end as scene_name
, case when 'total' in ({select_ui_type}) then '合计' else cast(ui_type as string) end as ui_type
, case when 'total' in ({select_start_network}) then '合计' else start_network end as 网络类型
, case when 'total' in ({select_down_result}) then '合计' else downloadResult end as 下载结果
, case when 'total' in ({select_down_error_code}) then '合计' else cast(down_error_code as string) end as 下载失败错误码
, case when 'total' in ({select_install_result}) then '合计' when AppSuccInstall=1 then 'succ' else 'not_succ' end as 安装结果
, case when 'total' in ({select_down_type}) then '合计' else down_type end as 下载类型
, case when 'total' in ({select_is_fast_install}) then '合计' when is_fast_install=1 then '是' else '否' end as 是否快速安装
, case when 'total' in ({select_is_front_down_succ}) then '合计' when is_front_down_succ=1 then '是' else '否' end as 是否前台下载完成
, case when 'total' in ({select_queue_is_yyb_front}) then '合计' when queue_is_yyb_front=1 then '是' else '否' end as 是否前台入队
, case when 'total' in ({select_is_screen_locked_down_succ}) then '合计' when is_screen_locked_down_succ=1 then '是' else '否' end as 是否关屏下载完成
, case when 'total' in ({select_micro_pkg}) then '合计' else micro_pkg end as 微端包
, case when 'total' in ({select_queue_is_screen_locked}) then '合计' when queue_is_screen_locked=1 then '是' else '否' end as 是否关屏入队
, case when 'total' in ({select_install_abort_msg}) then '合计' else install_abort_msg end as 安装失败原因
, case when 'total' in ({select_call_error_code_list}) then '合计' else call_error_code_list end as 安装调用结果码

, case 
      when {string_tab_1} = 0 and {string_tab_2} = 0 then '合计' 
      when switch_code_list like '%{string_tab_1}%' then '对照组{string_tab_1}'
      when switch_code_list like '%{string_tab_2}%' then '实验组{string_tab_2}'
      else '未分组' end as 实验分组

, count(1) 开始下载数
, sum(if(downloadResult='succ', 1, 0)) 下载成功数
, sum(AppSuccInstall) 安装成功数

, round(sum(if(downloadResult='succ', 1, 0)) * 100 / count(1), 2) 下载CVR
, round(sum(AppSuccInstall) * 100 / sum(if(downloadResult='succ', 1, 0)), 2) 安装CVR
, round(sum(AppSuccInstall) * 100 / count(1), 2) 下载安装CVR
, sum(is_fast_install) 快速安装数
, round(sum(is_fast_install) * 100 / sum(is_fast_install_config), 2) 快速安装率
, round(sum(succ_is_swapped_apk) * 100 / sum(AppSuccInstall), 2) 洗包率

, round(sum(if(PhantomStartInstall=1 or AppInstallRealStart=1, 1, 0)) * 100 / sum(if(downloadResult='succ', 1, 0)), 2) 拉起系统安装率
, round(sum(AppSuccInstall) * 100 / sum(if(PhantomStartInstall=1 or AppInstallRealStart=1, 1, 0)), 2) 系统安装完成率

, round(sum(if(downloadResult='del', 1, 0)) * 100 / count(1), 2) 下载删除率
, round(sum(if(downloadResult='pause', 1, 0)) * 100 / count(1), 2) 下载暂停率
, round(sum(if(downloadResult='fail', 1, 0)) * 100 / count(1), 2) 下载失败率
, round(sum(if(downloadResult='unfinish', 1, 0)) * 100 / count(1), 2) 下载未完率

, round(sum(if(cast(evil_level as int) > 2,1,0)) * 100 / count(1), 2) 恶意占比
, round(sum(if(down_type='预约静默下载',1,0)) * 100 / count(1), 2) 预约静默下载占比
, round(sum(is_front_down_succ) * 100 / sum(if(downloadResult='succ', 1, 0)), 2) 下载成功前台率
, round(sum(queue_is_yyb_front) * 100 / sum(AppBeginInstall_Queue), 2) 安装入队前台率
, round(sum(queue_is_screen_locked) * 100 / sum(AppBeginInstall_Queue), 2) 安装入队关屏率
, round(sum(if(micro_pkg='微端包',1,0)) * 100 / sum(if(downloadResult='succ', 1, 0)), 2) 微端比例

, round(sum(if(retry_type='f' or retry_type='b',1,0)) * 100 / count(1), 2) 安装重试率
, round(sum(if(retry_type='f',1,0)) * 100 / count(1), 2) 前台安装重试率
, round(sum(if(retry_type='f' or retry_type='b',1,0)) * 100 / sum(AppSuccInstall), 2) 安装成功重试率
, round(sum(AppBeginInstall_Call) * 100 / count(1), 2) AppBeginInstall_Call
, round(sum(AppBeginInstall_Queue) * 100 / count(1), 2) AppBeginInstall_Queue
, round(sum(AppInstallListenerRequest) * 100 / count(1), 2) AppInstallListenerRequest
, round(sum(AppInstallListenerExposure) * 100 / count(1), 2) AppInstallListenerExposure
, round(sum(PhantomStartCheck) * 100 / count(1), 2) PhantomStartCheck
, round(sum(PhantomStartInstall) * 100 / count(1), 2) PhantomStartInstall
, round(sum(AppInstallRealStart) * 100 / count(1), 2) AppInstallRealStart
, round(sum(AppInstallSessionFinished) * 100 / count(1), 2) AppInstallSessionFinished
, round(sum(AppSuccInstall) * 100 / count(1), 2) AppSuccInstall
, round(sum(AppInstallSessionCancel) * 100 / count(1), 2) AppInstallSessionCancel
, round(sum(if(AppSuccInstall=1 or AppInstallSessionCancel=1, 0, AppFailInstall)) * 100 / count(1), 2) AppFailInstall

, round(sum(if(downloadResult='succ', download_speed, 0))/1024/1024 / sum(if(downloadResult='succ', 1, 0)), 2) 平均速度MB
, round(ApproxPercentile(case when downloadResult='succ' then download_speed else null end, 0.5)/1024/1024, 2)  as 速度P50
, round(ApproxPercentile(case when downloadResult='succ' then download_speed else null end, 0.75)/1024/1024, 2)  as 速度P75
, round(ApproxPercentile(case when downloadResult='succ' then download_speed else null end, 0.90)/1024/1024, 2)  as 速度P90
, round(ApproxPercentile(case when downloadResult='succ' then download_speed else null end, 0.95)/1024/1024, 2)  as 速度P95
, round(ApproxPercentile(case when downloadResult='succ' then download_speed else null end, 0.99)/1024/1024, 2)  as 速度P99
, round(ApproxPercentile(case when downloadResult='succ' then download_speed else null end, 0.999)/1024/1024, 2)  as 速度P999
, round(ApproxPercentile(case when downloadResult='succ' then download_speed else null end, 0.9999)/1024/1024, 2)  as 速度P9999
, round(ApproxPercentile(case when downloadResult='succ' then download_speed else null end, 0.99999)/1024/1024, 2)  as 速度P99999
, round(sum(if(downloadResult='succ', is_buffer_full, 0)) / sum(if(downloadResult='succ', 1, 0)), 4) buffer溢出率


from (
select
  ds
  , a.download_id download_id
  , a.guid guid
  , a.evil_level evil_level
  , a.sim_download_id sim_download_id
  , a.appid appid
  , a.package_name package_name
  , a.app_name app_name
  , a.scene scene
  , a.scene_name scene_name
  , a.ui_type ui_type
  , a.brand brand
  , a.os_version os_version
  , a.qua_big_version qua_big_version
  , a.qua qua
  , a.start_network start_network
  , a.game_coop_type game_coop_type
  , a.is_booking_pop_ui_task is_booking_pop_ui_task
  , a.is_booking_task_bound is_booking_task_bound

  , b.downloadResult downloadResult
  , b.switch_code_list switch_code_list
  , b.AppBeginInstall_Queue AppBeginInstall_Queue
  , b.AppSuccInstall AppSuccInstall
  , b.AppFailInstall AppFailInstall
  , b2.down_error_code down_error_code

  , c.is_front_down_succ is_front_down_succ
  , c.is_screen_locked_down_succ is_screen_locked_down_succ
  , a.micro_pkg micro_pkg
  , c.download_speed download_speed
  , c.is_buffer_full is_buffer_full

  , cc.succ_install_mode succ_install_mode
  , cc.succ_is_swapped_apk succ_is_swapped_apk

  , d.AppInstallSessionProceed AppInstallSessionProceed
  , d.AppInstallSessionCreated AppInstallSessionCreated
  , d.AppInstallSessionProgress AppInstallSessionProgress
  , d.AppInstallSessionFinished AppInstallSessionFinished
  , d.AppInstallSessionCancel AppInstallSessionCancel
  , d.AppInstallListenerRequest AppInstallListenerRequest
  , d.AppInstallListenerExposure AppInstallListenerExposure
  , d.AppInstallRealStart AppInstallRealStart
  , d.AppBeginInstall_Call AppBeginInstall_Call
  , d.AppPureStartInstall AppPureStartInstall
  , d.AppSystemStartInstall AppSystemStartInstall
  , d.AppSystemInstallHeartBeat AppSystemInstallHeartBeat
  , d.PhantomStartCheck PhantomStartCheck
  , d.PhantomStartInstall PhantomStartInstall
  , d.PhantomAccountReceive PhantomAccountReceive

  , d2.call_error_code_list call_error_code_list

  , e.phantom_install_type phantom_install_type
  , e.phantom_error_code phantom_error_code
  , e.phantom_pull_type phantom_pull_type
  , e.is_fast_install_config is_fast_install_config
  , e.is_fast_install as is_fast_install

  , ee.queue_is_yyb_front queue_is_yyb_front
  , ee.queue_is_screen_locked queue_is_screen_locked

  , f.retry_type retry_type

  , g.install_abort_msg install_abort_msg

  , case
      when a.scene = '-1000' then '买量下载'
      when a.ui_type = 0 and (a.is_booking_pop_ui_task=1 or a.is_booking_task_bound = 1) then '预约静默下载'
      when a.ui_type = 0 and a.is_booking_pop_ui_task is null and a.is_booking_task_bound is null then '普通下载'
      else '其他下载'
      end
      as down_type

from
(
    select
        case when 'total' in ({select_expand_ds}) then '合计' else cast(ds as string) end as ds
        , concat(guid, download_id) download_id
        , max(guid) guid
        , nvl(max(evil_level),'0') evil_level
        , max(download_id) sim_download_id
        , max(appid) appid
        , max(package_name) package_name
        , max(appname) app_name
        , max(scene) scene
        , max(scene_name) scene_name
        , max(ui_type) ui_type
        , max(case when yyb_brand not in ('huawei','xiaomi','redmi','oneplus','vivo','realme','honor','sprd','samsung','oppo','5g') then '其他' else yyb_brand end) brand
        , max(split_part(android_version,'.',1)) os_version
        , max(substr(qua, 6, 3)) qua_big_version
        , max(qua) qua
        , max(access_network) start_network
        , max(game_coop_type) game_coop_type
        , max(case when instr(event_value,'auto_down_patch_uuid')>0 then 1 end) is_booking_pop_ui_task
        , max(case when instr(event_value,'is_set_local_copy')>0 or instr(event_value,'is_set_down_proxy')>0 then 1 end) is_booking_task_bound
        , max(case when split_part(regexp_extract(event_value, 'enable_dual_down=(.*)', 1), '&', 1)='1' then 1 end) as enable_dual_down
        , case when max(appsize) > 1552388158 then '完整包' when max(appsize)<786499773 then '未知' else '微端包' end as micro_pkg
    from
        beacon_olap.dwd_yyb_event_log_app_event_mid_di
    where
        ds between {tr1_start} and {tr1_end}
        and case when 'total' in ({select_qua_big_version}) then 1=1 else substr(qua, 6, 3) in ({select_qua_big_version}) end
        and case when 'total' in ({select_qua}) then 1=1 else qua in ({select_qua}) end
        and case when 'total' in ({select_brand}) then 1=1 when 'expand' in ({select_brand}) then 1=1 else yyb_brand in ({select_brand}) end
        and case when 'total' in ({select_appid}) then 1=1 else cast(appid as string) in ({select_appid}) end
        and case when 'total' in ({select_package_name}) then 1=1 else package_name in ({select_package_name}) end
        and case when 'total' in ({select_app_name}) then 1=1 else appname in ({select_app_name}) end
        and case when 'total' in ({select_game_coop_type}) then 1=1 else game_coop_type in ({select_game_coop_type}) end
        and case when 'total' in ({select_expand_guid}) then 1=1 else guid in ({select_expand_guid}) end
        and case when 'total' in ({select_evil_level}) then 1=1 when 'expand' in ({select_evil_level}) then 1=1 when 'filter' in ({select_evil_level}) then evil_level not in ('3','4') else evil_level in ({select_evil_level}) end
        and case when 'total' in ({select_os_version}) then 1=1 else split_part(android_version,'.',1) in ({select_os_version}) end
        and case when 'total' in ({select_scene}) then 1=1 else scene in ({select_scene}) end
        and case when 'total' in ({select_scene_name}) then 1=1 else scene_name in ({select_scene_name}) end
        and case when 'total' in ({select_ui_type}) then 1=1 else cast(ui_type as string) in ({select_ui_type}) end
        and case when 'total' in ({select_start_network}) then 1=1 else access_network in ({select_start_network}) end
        and event_code in ('AppStartDownload')
        and instr(event_value,'booking_pre_pkg_type')<1 --去掉预约静默资源包下载
    group by 
        ds, download_id
)a --开始下载，多维度

left join(
    select
        download_id
        , case  when event_code_list like '%AppSuccDownload%' then 'succ'
                when event_code_list like '%AppDelDownload%' then 'del'
                when event_code_list like '%AppFailDownload%' then 'fail'
                when event_code_list like '%AppPauseDownload%' then 'pause'
                else 'unfinish' end as downloadResult
        , switch_code_list as switch_code_list
        , case when event_code_list like '%AppBeginInstall%' then 1 end as AppBeginInstall_Queue
        , case when event_code_list like '%AppSuccInstall%' then 1 else 0 end as AppSuccInstall
        , case when event_code_list like '%AppFailInstall%' then 1 end as AppFailInstall
    from(
        select
            concat(guid, download_id) download_id
            , group_concat(distinct event_code) as event_code_list
            , group_concat(distinct switch_code) as switch_code_list

        from
            beacon_olap.dwd_yyb_event_log_app_event_mid_di
        where
            ds between {tr1_start} and {tr1_end}
            and case when 'total' in ({select_qua_big_version}) then 1=1 else substr(qua, 6, 3) in ({select_qua_big_version}) end
            and case when 'total' in ({select_qua}) then 1=1 else qua in ({select_qua}) end
            and case when 'total' in ({select_brand}) then 1=1 when 'expand' in ({select_brand}) then 1=1 else yyb_brand in ({select_brand}) end
            and case when 'total' in ({select_appid}) then 1=1 else cast(appid as string) in ({select_appid}) end
            and case when 'total' in ({select_package_name}) then 1=1 else package_name in ({select_package_name}) end
            and case when 'total' in ({select_app_name}) then 1=1 else appname in ({select_app_name}) end
            and case when 'total' in ({select_game_coop_type}) then 1=1 else game_coop_type in ({select_game_coop_type}) end
            and case when 'total' in ({select_expand_guid}) then 1=1 else guid in ({select_expand_guid}) end
            and case when 'total' in ({select_evil_level}) then 1=1 when 'expand' in ({select_evil_level}) then 1=1 when 'filter' in ({select_evil_level}) then evil_level not in ('3','4') else evil_level in ({select_evil_level}) end
            and case when 'total' in ({select_os_version}) then 1=1 else split_part(android_version,'.',1) in ({select_os_version}) end
            and case when 'total' in ({select_scene}) then 1=1 else scene in ({select_scene}) end
            and case when 'total' in ({select_scene_name}) then 1=1 else scene_name in ({select_scene_name}) end
            and case when 'total' in ({select_ui_type}) then 1=1 else cast(ui_type as string) in ({select_ui_type}) end
            and case when 'total' in ({select_start_network}) then 1=1 else access_network in ({select_start_network}) end

            and event_code in ('AppPauseDownload','AppSuccDownload','AppFailDownload','AppDelDownload','AppBeginInstall','AppSuccInstall','AppFailInstall')
        group by
            download_id
    )in_b
)b --下载结果/实验id
on a.download_id = b.download_id

left join(
    select
        concat(guid, download_id) download_id
        , max(cast(split_part(regexp_extract(event_value, 'taskerror=(.*)', 1), '&', 1) as bigint)) down_error_code

    from
        beacon_olap.dwd_yyb_event_log_app_event_mid_di
    where
        ds between {tr1_start} and {tr1_end}
        and case when 'total' in ({select_qua_big_version}) then 1=1 else substr(qua, 6, 3) in ({select_qua_big_version}) end
        and case when 'total' in ({select_qua}) then 1=1 else qua in ({select_qua}) end
        and case when 'total' in ({select_brand}) then 1=1 when 'expand' in ({select_brand}) then 1=1 else yyb_brand in ({select_brand}) end
        and case when 'total' in ({select_appid}) then 1=1 else cast(appid as string) in ({select_appid}) end
        and case when 'total' in ({select_package_name}) then 1=1 else package_name in ({select_package_name}) end
        and case when 'total' in ({select_app_name}) then 1=1 else appname in ({select_app_name}) end
        and case when 'total' in ({select_game_coop_type}) then 1=1 else game_coop_type in ({select_game_coop_type}) end
        and case when 'total' in ({select_expand_guid}) then 1=1 else guid in ({select_expand_guid}) end
        and case when 'total' in ({select_evil_level}) then 1=1 when 'expand' in ({select_evil_level}) then 1=1 when 'filter' in ({select_evil_level}) then evil_level not in ('3','4') else evil_level in ({select_evil_level}) end
        and case when 'total' in ({select_os_version}) then 1=1 else split_part(android_version,'.',1) in ({select_os_version}) end
        and case when 'total' in ({select_scene}) then 1=1 else scene in ({select_scene}) end
        and case when 'total' in ({select_scene_name}) then 1=1 else scene_name in ({select_scene_name}) end
        and case when 'total' in ({select_ui_type}) then 1=1 else cast(ui_type as string) in ({select_ui_type}) end
        and case when 'total' in ({select_down_error_code}) then 0=1 else 1=1 end  --仅在展开下载错误码时才需要查询

        and event_code in ('AppFailDownload')
    group by
        download_id
)b2 --下载失败错误码
on b2.download_id = b.download_id and b.downloadResult='fail'

left join(
    select
        concat(guid, download_id) download_id
        , max(is_yyb_front) as is_front_down_succ
        , max(cast(split_part(regexp_extract(event_value, 'is_screen_locked=(.*)', 1), '&', 1) as int)) is_screen_locked_down_succ
        , max(case when split_part(regexp_extract(event_value, 'is_dual_down=(.*)', 1), '&', 1)='1' then 1 end) as is_dual_down
        , max(cast(split_part(regexp_extract(event_value, 'download_speed=(.*)', 1), '&', 1) as int)) download_speed
        , max(cast(split_part(regexp_extract(event_value, 'down_hl_is_buffer_full=(.*)', 1), '&', 1) as int)) is_buffer_full

    from
        beacon_olap.dwd_yyb_event_log_app_event_mid_di
    where
        ds between {tr1_start} and {tr1_end}
        and case when 'total' in ({select_qua_big_version}) then 1=1 else substr(qua, 6, 3) in ({select_qua_big_version}) end
        and case when 'total' in ({select_qua}) then 1=1 else qua in ({select_qua}) end
        and case when 'total' in ({select_brand}) then 1=1 when 'expand' in ({select_brand}) then 1=1 else yyb_brand in ({select_brand}) end
        and case when 'total' in ({select_appid}) then 1=1 else cast(appid as string) in ({select_appid}) end
        and case when 'total' in ({select_package_name}) then 1=1 else package_name in ({select_package_name}) end
        and case when 'total' in ({select_app_name}) then 1=1 else appname in ({select_app_name}) end
        and case when 'total' in ({select_game_coop_type}) then 1=1 else game_coop_type in ({select_game_coop_type}) end
        and case when 'total' in ({select_expand_guid}) then 1=1 else guid in ({select_expand_guid}) end
        and case when 'total' in ({select_evil_level}) then 1=1 when 'expand' in ({select_evil_level}) then 1=1 when 'filter' in ({select_evil_level}) then evil_level not in ('3','4') else evil_level in ({select_evil_level}) end
        and case when 'total' in ({select_os_version}) then 1=1 else split_part(android_version,'.',1) in ({select_os_version}) end
        and case when 'total' in ({select_scene}) then 1=1 else scene in ({select_scene}) end
        and case when 'total' in ({select_scene_name}) then 1=1 else scene_name in ({select_scene_name}) end
        and case when 'total' in ({select_ui_type}) then 1=1 else cast(ui_type as string) in ({select_ui_type}) end
        and case when 'total' in ({select_start_network}) then 1=1 else access_network in ({select_start_network}) end
        and event_code in ('AppSuccDownload')
    group by
        ds, download_id
)c --下载成功特有属性：后台下载，本地拷贝，双通道，网络类型
on a.download_id = c.download_id

left join(
    select
        concat(guid, download_id) download_id
        , max(cast(split_part(regexp_extract(event_value, 'install_mode=(.*)', 1), '&', 1) as integer)) as succ_install_mode
        , max(case when split_part(regexp_extract(event_value, 'is_swapped_apk=(.*)', 1), '&', 1)='1' then 1 end) as succ_is_swapped_apk

    from
        beacon_olap.dwd_yyb_event_log_app_event_mid_di
    where
        ds between {tr1_start} and {tr1_end}
        and case when 'total' in ({select_qua_big_version}) then 1=1 else substr(qua, 6, 3) in ({select_qua_big_version}) end
        and case when 'total' in ({select_qua}) then 1=1 else qua in ({select_qua}) end
        and case when 'total' in ({select_brand}) then 1=1 when 'expand' in ({select_brand}) then 1=1 else yyb_brand in ({select_brand}) end
        and case when 'total' in ({select_appid}) then 1=1 else cast(appid as string) in ({select_appid}) end
        and case when 'total' in ({select_package_name}) then 1=1 else package_name in ({select_package_name}) end
        and case when 'total' in ({select_app_name}) then 1=1 else appname in ({select_app_name}) end
        and case when 'total' in ({select_game_coop_type}) then 1=1 else game_coop_type in ({select_game_coop_type}) end
        and case when 'total' in ({select_expand_guid}) then 1=1 else guid in ({select_expand_guid}) end
        and case when 'total' in ({select_evil_level}) then 1=1 when 'expand' in ({select_evil_level}) then 1=1 when 'filter' in ({select_evil_level}) then evil_level not in ('3','4') else evil_level in ({select_evil_level}) end
        and case when 'total' in ({select_os_version}) then 1=1 else split_part(android_version,'.',1) in ({select_os_version}) end
        and case when 'total' in ({select_scene}) then 1=1 else scene in ({select_scene}) end
        and case when 'total' in ({select_scene_name}) then 1=1 else scene_name in ({select_scene_name}) end
        and case when 'total' in ({select_ui_type}) then 1=1 else cast(ui_type as string) in ({select_ui_type}) end
        and case when 'total' in ({select_start_network}) then 1=1 else access_network in ({select_start_network}) end
        and event_code in ('AppSuccInstall')
    group by
        ds, download_id
)cc --安装成功特有属性：安装类型、洗包
on a.download_id = cc.download_id

left join(
    select
        download_id
        , case when event_code_list like '%AppInstallSessionProceed%' then 1 end as AppInstallSessionProceed
        , case when event_code_list like '%AppInstallSessionCreated%' then 1 end as AppInstallSessionCreated
        , case when event_code_list like '%AppInstallSessionProgress%' then 1 end as AppInstallSessionProgress
        , case when event_code_list like '%AppInstallSessionFinished%' then 1 end as AppInstallSessionFinished
        , case when event_code_list like '%AppInstallSessionCancel%' then 1 end as AppInstallSessionCancel
        , case when event_code_list like '%AppInstallListenerRequest%' then 1 end as AppInstallListenerRequest
        , case when event_code_list like '%AppInstallListenerExposure%' then 1 end as AppInstallListenerExposure
        , case when event_code_list like '%AppInstallRealStart%' then 1 end as AppInstallRealStart
        , case when event_code_list like '%AppBeginInstall%' then 1 end as AppBeginInstall_Call
        , case when event_code_list like '%AppPureStartInstall%' then 1 end as AppPureStartInstall
        , case when event_code_list like '%AppSystemStartInstall%' then 1 end as AppSystemStartInstall
        , case when event_code_list like '%AppSystemInstallHeartBeat%' then 1 end as AppSystemInstallHeartBeat
        , case when event_code_list like '%PhantomStartCheck%' then 1 end as PhantomStartCheck
        , case when event_code_list like '%PhantomStartInstall%' then 1 end as PhantomStartInstall
        , case when event_code_list like '%PhantomAccountReceive%' then 1 end as PhantomAccountReceive
    from(
        select
            group_concat(distinct event_name) as event_code_list
            , concat(guid, download_id) download_id

        from
            [1055717].[dws_ydc_dload_install_hi]
        where
            ds between cast(concat(cast({tr1_start} as string),'00') as bigint) and cast(concat(cast({tr1_end} as string),'23') as bigint)
            and case when 'total' in ({select_qua_big_version}) then 1=1 else substr(qua, 6, 3) in ({select_qua_big_version}) end
            and case when 'total' in ({select_qua}) then 1=1 else qua in ({select_qua}) end
            and case when 'total' in ({select_brand}) then 1=1 when 'expand' in ({select_brand}) then 1=1 else yyb_brand in ({select_brand}) end
            and case when 'total' in ({select_package_name}) then 1=1 else package_name in ({select_package_name}) end
            and case when 'total' in ({select_expand_guid}) then 1=1 else guid in ({select_expand_guid}) end
            and case when 'total' in ({select_os_version}) then 1=1 else split_part(android_version,'.',1) in ({select_os_version}) end
            and event_name in (
                'AppInstallSessionProceed',
                'AppInstallSessionCreated',
                'AppInstallSessionProgress',
                'AppInstallSessionFinished',
                'AppInstallSessionCancel',
                'AppInstallListenerRequest',
                'AppInstallListenerExposure',
                'AppInstallRealStart',

                'AppBeginInstall',
                'AppPureStartInstall',
                'AppSystemStartInstall',
                'AppSystemInstallHeartBeat',

                'PhantomStartCheck',
                'PhantomStartInstall',
                'PhantomAccountReceive'
            )
        group by
            download_id
    )in_d
)d --安装链路，不过滤条件
on a.download_id = d.download_id

left join(
    select
        download_id
        , error_code_list as call_error_code_list
    from(
        select
            group_concat(distinct cast(error_code as string)) as error_code_list
            , concat(guid, download_id) download_id

        from
            [1055717].[dws_ydc_dload_install_hi]
        where
            ds between cast(concat(cast({tr1_start} as string),'00') as bigint) and cast(concat(cast({tr1_end} as string),'23') as bigint)
            and case when 'total' in ({select_qua_big_version}) then 1=1 else substr(qua, 6, 3) in ({select_qua_big_version}) end
            and case when 'total' in ({select_qua}) then 1=1 else qua in ({select_qua}) end
            and case when 'total' in ({select_brand}) then 1=1 when 'expand' in ({select_brand}) then 1=1 else yyb_brand in ({select_brand}) end
            and case when 'total' in ({select_package_name}) then 1=1 else package_name in ({select_package_name}) end
            and case when 'total' in ({select_expand_guid}) then 1=1 else guid in ({select_expand_guid}) end
            and case when 'total' in ({select_os_version}) then 1=1 else split_part(android_version,'.',1) in ({select_os_version}) end
            and case when 'total' in ({select_call_error_code_list}) then 0=1 else 1=1 end  --仅在展开调用错误码时才需要查询
            and event_name in (
                'AppBeginInstall'
            )
        group by
            download_id
    )in_d2
)d2 --安装调用结果码
on a.download_id = d2.download_id

left join
(
    select
        download_id
        , queue_is_yyb_front
        , case when is_screen_locked_list like '%0%' then 0 when is_screen_locked_list like '%1%' then 1 else 0 end as queue_is_screen_locked
    from(
        select
            concat(guid, download_id) download_id
            , max(is_yyb_front) queue_is_yyb_front
            , group_concat(distinct split_part(regexp_extract(event_value, 'is_screen_locked=(.*)', 1), '&', 1)) as is_screen_locked_list
        from
            beacon_olap.dwd_yyb_event_log_app_event_mid_di
        where
            ds between {tr1_start} and {tr1_end}
            and case when 'total' in ({select_brand}) then 1=1 when 'expand' in ({select_brand}) then 1=1 else yyb_brand in ({select_brand}) end
            and case when 'total' in ({select_appid}) then 1=1 else cast(appid as string) in ({select_appid}) end
            and case when 'total' in ({select_package_name}) then 1=1 else package_name in ({select_package_name}) end
            and case when 'total' in ({select_app_name}) then 1=1 else appname in ({select_app_name}) end
            and case when 'total' in ({select_game_coop_type}) then 1=1 else game_coop_type in ({select_game_coop_type}) end
            and case when 'total' in ({select_expand_guid}) then 1=1 else guid in ({select_expand_guid}) end
            and case when 'total' in ({select_evil_level}) then 1=1 when 'expand' in ({select_evil_level}) then 1=1 when 'filter' in ({select_evil_level}) then evil_level not in ('3','4') else evil_level in ({select_evil_level}) end
            and case when 'total' in ({select_os_version}) then 1=1 else split_part(android_version,'.',1) in ({select_os_version}) end
            and case when 'total' in ({select_scene}) then 1=1 else scene in ({select_scene}) end
            and case when 'total' in ({select_scene_name}) then 1=1 else scene_name in ({select_scene_name}) end
            and case when 'total' in ({select_ui_type}) then 1=1 else cast(ui_type as string) in ({select_ui_type}) end

            and event_code in ('AppBeginInstall')
        group by
            download_id
        )in_ee
)ee --安装入队时状态：关屏，锁屏，后台
on a.download_id = ee.download_id

left join
(
    select
        concat(guid, download_id) download_id
        , max(install_type) as phantom_install_type
        , max(error_code) as phantom_error_code
        , max(pull_type) as phantom_pull_type
        , max(case when install_mode = 0 and error_code <> -1 then 1 else 0 end) as is_fast_install_config
        , max(case when install_mode = 0 and install_type = 8 and error_code = 0 and pull_type in (1, 3, 4) then 1 else 0 end) as is_fast_install
    from
        [1055717].[dws_ydc_dload_install_hi]
    where
        ds between cast(concat(cast({tr1_start} as string),'00') as bigint) and cast(concat(cast({tr1_end} as string),'23') as bigint)
        and case when 'total' in ({select_qua_big_version}) then 1=1 else substr(qua, 6, 3) in ({select_qua_big_version}) end
        and case when 'total' in ({select_brand}) then 1=1 when 'expand' in ({select_brand}) then 1=1 else yyb_brand in ({select_brand}) end
        and case when 'total' in ({select_package_name}) then 1=1 else package_name in ({select_package_name}) end
        and case when 'total' in ({select_expand_guid}) then 1=1 else guid in ({select_expand_guid}) end
        and case when 'total' in ({select_os_version}) then 1=1 else split_part(android_version,'.',1) in ({select_os_version}) end
        and event_name in ('PhantomStartInstall')
    group by
        download_id
)e --安装类型
on a.download_id = e.download_id

left join
(
    select
        download_id
        , case when is_yyb_front_list like '%1%' then 'f' else 'b' end as retry_type
    from(
        select
            concat(guid, download_id) as download_id
            , group_concat(distinct cast(is_yyb_front as string)) as is_yyb_front_list
        from
            beacon_olap.dwd_yyb_event_log_app_event_mid_di
        where
            ds between {tr1_start} and {tr1_end}
            and case when 'total' in ({select_qua_big_version}) then 1=1 else substr(qua, 6, 3) in ({select_qua_big_version}) end
            and case when 'total' in ({select_qua}) then 1=1 else qua in ({select_qua}) end
            and case when 'total' in ({select_brand}) then 1=1 when 'expand' in ({select_brand}) then 1=1 else yyb_brand in ({select_brand}) end
            and case when 'total' in ({select_appid}) then 1=1 else cast(appid as string) in ({select_appid}) end
            and case when 'total' in ({select_package_name}) then 1=1 else package_name in ({select_package_name}) end
            and case when 'total' in ({select_app_name}) then 1=1 else appname in ({select_app_name}) end
            and case when 'total' in ({select_game_coop_type}) then 1=1 else game_coop_type in ({select_game_coop_type}) end
            and case when 'total' in ({select_expand_guid}) then 1=1 else guid in ({select_expand_guid}) end
            and case when 'total' in ({select_evil_level}) then 1=1 when 'expand' in ({select_evil_level}) then 1=1 when 'filter' in ({select_evil_level}) then evil_level not in ('3','4') else evil_level in ({select_evil_level}) end
            and case when 'total' in ({select_os_version}) then 1=1 else split_part(android_version,'.',1) in ({select_os_version}) end
            and case when 'total' in ({select_scene}) then 1=1 else scene in ({select_scene}) end
            and case when 'total' in ({select_scene_name}) then 1=1 else scene_name in ({select_scene_name}) end
            and case when 'total' in ({select_ui_type}) then 1=1 else cast(ui_type as string) in ({select_ui_type}) end
            and case when 'total' in ({select_start_network}) then 1=1 else access_network in ({select_start_network}) end
            and event_code in ('AppBeginInstall')
            and instr(event_value, 'install_retried')>0
        group by
            download_id
    )in_f
)f --安装重试
on a.download_id = f.download_id

left join
(
  select
    concat(guid, download_id) as download_id
    , nvl(max(split_part(abort_msg,'[',1)), 'empty') install_abort_msg
  from
      [1055717].[yyb_internal_install]
  where
      event_name in ('install_cancel','install_fail')
      and ds between cast(concat(cast({tr1_start} as string),'00') as bigint) and cast(concat(cast({tr1_end} as string),'23') as bigint)
      and case when 'total' in ({select_brand}) then 1=1 when 'expand' in ({select_brand}) then 1=1 else yyb_brand in ({select_brand}) end
      and case when 'total' in ({select_package_name}) then 1=1 else package_name in ({select_package_name}) end
      and case when 'total' in ({select_app_name}) then 1=1 else appname in ({select_app_name}) end
      and case when 'total' in ({select_expand_guid}) then 1=1 else guid in ({select_expand_guid}) end
      and case when 'total' in ({select_os_version}) then 1=1 else split_part(android_version,'.',1) in ({select_os_version}) end
      and case when 'total' in ({select_scene}) then 1=1 else scene in ({select_scene}) end
      and case when 'total' in ({select_install_abort_msg}) then 0=1 else 1=1 end  --仅在展开安装失败原因时才需要查询
  group by download_id
)g --安装失败原因
on a.download_id = g.download_id

) source

where
case when 'total' in ({select_down_result}) then 1=1 else downloadResult in ({select_down_result}) end
and case when 'total' in ({select_install_result}) then 1=1 else cast(AppSuccInstall as string) in ({select_install_result}) end
and case when 'total' in ({select_down_type}) then 1=1 else down_type in ({select_down_type}) end
and case when 'total' in ({select_is_fast_install}) then 1=1 else cast(is_fast_install as string) in ({select_is_fast_install}) end
and case when 'total' in ({select_is_front_down_succ}) then 1=1 else cast(is_front_down_succ as string) in ({select_is_front_down_succ}) end
and case when 'total' in ({select_queue_is_yyb_front}) then 1=1 else cast(queue_is_yyb_front as string) in ({select_queue_is_yyb_front}) end
and case when 'total' in ({select_is_screen_locked_down_succ}) then 1=1 else cast(is_screen_locked_down_succ as string) in ({select_is_screen_locked_down_succ}) end
and case when 'total' in ({select_micro_pkg}) then 1=1 else micro_pkg in ({select_micro_pkg}) end
and case when 'total' in ({select_queue_is_screen_locked}) then 1=1 else cast(queue_is_screen_locked as string) in ({select_queue_is_screen_locked}) end
and case when 'total' in ({select_call_error_code_list}) then 1=1 when 'expand' in ({select_call_error_code_list}) then 1=1 else call_error_code_list in ({select_call_error_code_list}) end
and case when 'total' in ({select_install_abort_msg}) then 1=1 when 'expand' in ({select_install_abort_msg}) then 1=1 when {select_install_abort_msg} = '' then install_abort_msg is null else install_abort_msg in ({select_install_abort_msg}) end

group by 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29
order by 日期 desc,开始下载数 desc,下载成功数 desc,安装成功数 desc
"""

    @classmethod
    def get_download_install_cvr_sql(cls, **kwargs) -> str:
        """
        获取下载安装CVR查询 SQL

        :param kwargs: SQL参数字典，包含所有需要替换的参数
        :return: 格式化的 SQL 语句
        """
        # 设置默认值
        default_params = {
            'select_qua_big_version': "'total'",
            'select_qua': "'total'",
            'select_brand': "'total'",
            'select_appid': "'total'",
            'select_package_name': "'total'",
            'select_app_name': "'total'",
            'select_expand_guid': "'total'",
            'select_evil_level': "'total'",
            'select_expand_download_id': "'total'",
            'select_game_coop_type': "'total'",
            'select_os_version': "'total'",
            'select_scene': "'total'",
            'select_scene_name': "'total'",
            'select_ui_type': "'total'",
            'select_start_network': "'total'",
            'select_down_result': "'total'",
            'select_down_error_code': "'total'",
            'select_install_result': "'total'",
            'select_down_type': "'total'",
            'select_is_fast_install': "'total'",
            'select_is_front_down_succ': "'total'",
            'select_queue_is_yyb_front': "'total'",
            'select_is_screen_locked_down_succ': "'total'",
            'select_micro_pkg': "'total'",
            'select_queue_is_screen_locked': "'total'",
            'select_install_abort_msg': "'total'",
            'select_call_error_code_list': "'total'",
            'select_expand_ds': "'expand'",  # 改为expand以显示具体日期
            'string_tab_1': '0',
            'string_tab_2': '0',
            'tr1_start': '20250101',
            'tr1_end': '20250131'
        }

        # 合并用户提供的参数和默认参数
        params = {**default_params, **kwargs}

        return cls.DOWNLOAD_INSTALL_CVR_SQL_PART1.format(**params)
