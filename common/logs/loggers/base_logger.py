import os
import json
from typing import List, Optional
from datetime import datetime


class BaseLogger:
    """
    基础日志类，负责按日期写入和读取JSON Lines格式文件
    """

    def __init__(self, base_dir: str):
        """
        初始化，确保目录存在
        :param base_dir: 存储日志文件的目录
        """
        self.base_dir = base_dir

    def _get_file_path(self, date_str: str) -> str:
        """
        根据日期字符串获取对应日志文件路径
        :param date_str: 日期字符串，格式 "YYYYMMDD"
        :return: 文件路径
        """
        path = os.path.join(self.base_dir, f"{date_str}.log")
        return os.path.join(self.base_dir, f"{date_str}.log")

    def write(self, data: dict, dt: Optional[datetime] = None):
        """
        追加写入一条日志数据（字典形式）
        :param data: 要写入的数据字典
        :param dt: 数据对应的时间，默认当前UTC时间
        """
        os.makedirs(self.base_dir, exist_ok=True)
        date_str = dt.strftime("%Y%m%d") if dt else datetime.utcnow().strftime("%Y%m%d")
        filepath = self._get_file_path(date_str)
        with open(filepath, "a", encoding="utf-8") as f:
            f.write(json.dumps(data, ensure_ascii=False) + "\n")

    def read(self, date_str: str) -> List[dict]:
        """
        读取指定日期的所有日志数据，返回字典列表
        :param date_str: 日期字符串，格式 "YYYYMMDD"
        :return: 数据字典列表
        """
        os.makedirs(self.base_dir, exist_ok=True)
        filepath = self._get_file_path(date_str)
        if not os.path.exists(filepath):
            return []
        results = []
        with open(filepath, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                try:
                    results.append(json.loads(line))
                except json.JSONDecodeError:
                    # 忽略格式错误的行
                    continue
        return results



if __name__ == "__main__":
    logger = BaseLogger("test_logs")
    sample_data = {"msg": "hello", "value": 123}
    logger.write(sample_data)
    today = datetime.utcnow().strftime("%Y%m%d")
    read_data = logger.read(today)
    print(f"读取 {today} 的日志数据:", read_data)
