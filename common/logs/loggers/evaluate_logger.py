from typing import List, Optional, Tuple
from datetime import datetime, timedelta
import openpyxl
from openpyxl.utils import get_column_letter

from common.logs.loggers.base_logger import BaseLogger
from common.logs.loggers.models import Evaluate


class EvaluateLogger(BaseLogger):
    """
    评价日志管理类
    """

    def __init__(self, base_dir: str = "logger_data/evaluates"):
        super().__init__(base_dir)

    def write_evaluate(self, evaluate: Evaluate):
        """
        写入一条评价日志
        :param evaluate: Evaluate对象
        """
        self.write(evaluate.to_dict())

    def read_evaluates(self, date_str: str) -> List[Evaluate]:
        """
        读取指定日期的所有评价，返回 Evaluate 对象列表
        :param date_str: 日期字符串 "YYYYMMDD"
        :return: Evaluate对象列表
        """
        dicts = self.read(date_str)
        return [Evaluate.from_dict(d) for d in dicts]

    def count_evaluates_by_name(self, date_str: str, en_name: str) -> int:
        """
        统计指定日期某人的评价数量
        :param date_str: 日期字符串 "YYYYMMDD"
        :param en_name: 用户姓名
        :return: 评价数量
        """
        evaluates = self.read_evaluates(date_str)
        return sum(1 for r in evaluates if r.en_name == en_name)

    def get_evaluates_by_order(self, date_str: str, order_id: str) -> List[Evaluate]:
        """
        获取指定日期某订单的所有评价
        :param date_str: 日期字符串 "YYYYMMDD"
        :param order_id: 订单号
        :return: Evaluate对象列表
        """
        evaluates = self.read_evaluates(date_str)
        return [r for r in evaluates if r.order_id == order_id]

    def count_evaluates_in_range(self, start_date: str, end_date: str, en_name: Optional[str] = None) -> int:
        """
        统计时间范围内的评价数量，可按姓名过滤
        :param start_date: 起始日期 "YYYYMMDD"
        :param end_date: 结束日期 "YYYYMMDD"
        :param en_name: 用户姓名，默认统计所有
        :return: 评价数量
        """
        start_dt = datetime.strptime(start_date, "%Y%m%d")
        end_dt = datetime.strptime(end_date, "%Y%m%d")
        count = 0
        for i in range((end_dt - start_dt).days + 1):
            day_str = (start_dt + timedelta(days=i)).strftime("%Y%m%d")
            if en_name:
                count += self.count_evaluates_by_name(day_str, en_name)
            else:
                count += len(self.read_evaluates(day_str))
        return count
    
    def query_by_field(
        self,
        start_date: str,
        end_date: str,
        field_name: str,
        field_value,
    ) -> Tuple[int, List[Evaluate]]:
        """
        查询指定时间范围内，指定字段等于指定值的评价数量和评价列表
        """
        start_dt = datetime.strptime(start_date, "%Y%m%d")
        end_dt = datetime.strptime(end_date, "%Y%m%d")
        matched_evaluates = []
        for i in range((end_dt - start_dt).days + 1):
            day_str = (start_dt + timedelta(days=i)).strftime("%Y%m%d")
            evaluates = self.read_evaluates(day_str)
            for ev in evaluates:
                value = self._get_field_value(ev, field_name)
                if value == field_value:
                    matched_evaluates.append(ev)
        return len(matched_evaluates), matched_evaluates

    def _get_field_value(self, obj, field_name: str):
        attrs = field_name.split(".")
        val = obj
        for attr in attrs:
            if isinstance(val, dict):
                val = val.get(attr, None)
            else:
                val = getattr(val, attr, None)
            if val is None:
                break
        return val
    
    def export_query_to_excel(
        self,
        start_date: str,
        end_date: str,
        field_name: str,
        field_value,
        excel_path: str,
    ) -> int:
        count, evaluates = self.query_by_field(start_date, end_date, field_name, field_value)
        if count == 0:
            print("没有匹配的数据，Excel文件未生成。")
            return 0

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "查询结果"

        # 区分普通字段和字典字段
        first_obj = evaluates[0]
        base_fields = []
        dict_fields = {}

        for k, v in vars(first_obj).items():
            if isinstance(v, dict):
                dict_fields[k] = set(v.keys())
            else:
                base_fields.append(k)

        # 扫描所有对象，补充字典字段的所有键
        for obj in evaluates[1:]:
            for dict_field in dict_fields.keys():
                d = getattr(obj, dict_field, {})
                if isinstance(d, dict):
                    dict_fields[dict_field].update(d.keys())

        # 构造表头
        headers = base_fields[:]
        for dict_field, keys in dict_fields.items():
            for key in sorted(keys):
                headers.append(f"{dict_field}.{key}")

        ws.append(headers)

        # 写入数据
        for obj in evaluates:
            row = []
            for field in base_fields:
                val = getattr(obj, field, "")
                row.append(val)
            for dict_field, keys in dict_fields.items():
                d = getattr(obj, dict_field, {})
                for key in sorted(keys):
                    val = d.get(key, "")
                    row.append(val)
            ws.append(row)

        # 调整列宽
        for i, col in enumerate(headers, 1):
            max_length = max(
                len(str(cell.value)) if cell.value else 0
                for cell in ws[get_column_letter(i)]
            )
            adjusted_width = max_length + 2
            ws.column_dimensions[get_column_letter(i)].width = adjusted_width

        wb.save(excel_path)
        print(f"已将 {count} 条数据写入 Excel 文件：{excel_path}")
        return count

evaluate_logger = EvaluateLogger()

if __name__ == "__main__":
    now = datetime.now()

    # evaluate = Evaluate(
    #     timestamp=now.strftime("%Y%m%d-%H%M%S"),
    #     en_name="john",
    #     cn_name="张三",
    #     ticket_id="order456",
    #     evaluate="5星"
    # )
    # evaluate_logger.write_evaluate(evaluate)

    # today_str = now.strftime("%Y%m%d")
    # evaluates = evaluate_logger.read_evaluates(today_str)
    # print(f"{today_str} 评价列表:", evaluates)

    # count = evaluate_logger.count_evaluates_by_name(today_str, "张三")
    # print(f"{today_str} 张三的评价数量:", count)

    # start = now.strftime("%Y%m%d")
    # end = start
    # count, events = evaluate_logger.query_by_field(start, end, "evaluate", "5星")
    # print(f"查询到 {count} 条事件，内容如下:")
    # for e in events:
    #     print(e)
    
    evaluate_logger.export_query_to_excel(
        start_date="20250523",
        end_date="20250624",
        field_name="evaluate",
        field_value="5星",
        excel_path="good.xlsx"
    )
