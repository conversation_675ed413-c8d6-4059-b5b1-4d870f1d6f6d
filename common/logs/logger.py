# logger.py
import logging
from datetime import datetime
import os


class Logger:
    def __init__(self, name, level=logging.INFO):
        # 创建一个 logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)

        # 获取当前日期和时间
        current_datetime = datetime.now()
        # 格式化日期和时间
        formatted_datetime = current_datetime.strftime('%Y-%m-%d %H:%M:%S')
        # 创建一个 handler，用于写入日志文件
        if not os.path.exists(".log"):
            os.makedirs('.log')
        handler = logging.FileHandler(f'.log/{formatted_datetime}_{name}.log')

        # 设置日志记录格式
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s:\n%(message)s')
        handler.setFormatter(formatter)

        # 添加 handler 到 logger
        self.logger.addHandler(handler)

    def info(self, message):
        """记录信息级别的日志"""
        self.logger.info(message)

    def warning(self, message):
        """记录警告级别的日志"""
        self.logger.warning(message)

    def error(self, message):
        """记录错误级别的日志"""
        self.logger.error(message)


# 创建一个全局可用的日志实例
app_logger = Logger('LogAssistant')
