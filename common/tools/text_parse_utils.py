import re
import textwrap
import json
import ast
from dataclasses import dataclass, fields, MISSING, field
from typing import (
    Any, Dict, List, Optional, Tuple, Type, ClassVar, Set, get_origin, get_args
)

#from common.client.iwiki_client import IWikiClient  # iwiki_client导入了本包，避免循环导入
from common.error.detailed_value_error import raise_value_error, ErrorCode


class TextParseUtils:
    """
    文本处理工具类，提供文本解析、字段类型转换、代码块提取、JSON行解析等静态方法。
    设计为无状态工具类，所有方法均为静态方法，方便直接调用，无需实例化。
    """

    @staticmethod
    def _convert_value(value: Optional[str], field_type: Any, key_name: str = "") -> Any:
        """
        根据字段类型将字符串值转换为对应的Python类型。

        支持基础类型（str, int, float, bool）、Optional[T]、List、Dict、Set等复杂类型。

        :param value: 待转换的字符串值
        :param field_type: 目标字段类型（可能是泛型）
        :param key_name: key名称，用于判断是否需要字符串里的删除回车
        :return: 转换后的值，转换失败时返回原字符串或None
        """
        if value is None:
            return None

        # 处理代码块，如果是Prompt保留回车（\n）
        if value and isinstance(value, str) and value.lstrip().startswith('```'):
            if key_name == "prompt":
                value = TextParseUtils.extract_code_block(value)
            else:
                value = TextParseUtils.extract_code_block(value, strip_newlines=True)
                    

        origin = get_origin(field_type)  # 泛型的原始类型，如list, dict等
        args = get_args(field_type)      # 泛型参数，如List[str]中的str

        # 处理 Optional[T]，即 Union[T, NoneType]
        Union = getattr(__import__('typing'), 'Union', None)
        if origin is Union:
            # 过滤 NoneType，取第一个非NoneType类型
            non_none_args = [a for a in args if a is not type(None)]
            if len(non_none_args) == 1:
                # 递归转换为非None类型
                return TextParseUtils._convert_value(value, non_none_args[0])
            # 多类型Union暂不支持，直接返回字符串
            return value

        # 处理基础类型
        if field_type in {str, int, float, bool}:
            if field_type is bool:
                # bool类型特殊处理，判断字符串是否表示True/False
                if isinstance(value, str):
                    val_lower = value.strip().lower()
                    if val_lower in {"true", "1", "yes", "y", "是"}:
                        return True
                    elif val_lower in {"false", "0", "no", "n", "否"}:
                        return False
                    else:
                        # 无法判断，返回原字符串
                        return value
                else:
                    # 如果不是字符串，尝试直接转换为bool
                    return bool(value)
            try:
                return field_type(value)
            except (ValueError, TypeError):
                return value

        # 处理 List 类型
        if origin in {list, List}:
            try:
                # 尝试用 ast.literal_eval 解析字符串为Python对象
                parsed = ast.literal_eval(value)
                if isinstance(parsed, list):
                    # 如果是 List[Tuple[str, str]]，需要进一步转换
                    if args:
                        elem_type = args[0]
                        elem_origin = get_origin(elem_type)
                        elem_args = get_args(elem_type)
                        # 判断是否是 Tuple[str, str]
                        if elem_origin in {tuple, Tuple} and len(elem_args) == 2 and all(
                            t == str for t in elem_args
                        ):
                            new_list = []
                            for e in parsed:
                                if isinstance(e, tuple) and len(e) == 2 and all(isinstance(x, str) for x in e):
                                    new_list.append(e)
                                elif isinstance(e, (list, tuple)) and len(e) == 2:
                                    new_list.append((str(e[0]), str(e[1])))
                                else:
                                    # 不符合格式，跳过
                                    pass
                            return new_list
                    # 否则直接返回列表
                    return parsed
                else:
                    # 解析结果不是列表，返回原字符串
                    return value
            except Exception:
                return value

        # 处理 Dict 类型
        if origin in {dict, Dict}:
            try:
                parsed = ast.literal_eval(value)
                if isinstance(parsed, dict):
                    if args and len(args) == 2:
                        key_type, val_type = args
                        print(f"Dict键类型: {key_type}, 值类型: {val_type}")
                        new_dict = {}
                        for k, v in parsed.items():
                            new_key = TextParseUtils._convert_value(str(k), key_type)
                            # v可能是复杂类型，递归转换
                            if isinstance(v, (list, dict, set)):
                                new_val = TextParseUtils._convert_value(str(v), val_type)
                            else:
                                new_val = TextParseUtils._convert_value(v, val_type)
                            new_dict[new_key] = new_val
                        print(f"Dict转换结果: {new_dict}")
                        return new_dict
                    print("无键值类型参数，直接返回字典")
                    return parsed
                else:
                    return value
            except Exception as e:
                return value

        # 处理 Set 类型
        if origin in {set, Set}:
            try:
                parsed = ast.literal_eval(value)
                if isinstance(parsed, set):
                    if args:
                        elem_type = args[0]
                        new_set = set()
                        for e in parsed:
                            try:
                                new_set.add(elem_type(e))
                            except Exception:
                                new_set.add(e)
                        return new_set
                    return parsed
                else:
                    return value
            except Exception:
                return value

        # 其他类型暂不处理，返回原字符串
        return value

    @staticmethod
    def parse(text: str, config_cls: Type, section_prefix: str = "### "):
        """
        解析文本，提取指定格式的区块内容，映射到配置类字段并自动转换类型。

        文本格式示例：
        ### 标题1
        内容1
        ### 标题2
        内容2

        :param text: 输入文本
        :param config_cls: 目标配置类，必须包含 _title_map 和 _required_fields 属性
        :param section_prefix: 区块标题前缀，默认 "### "
        :return: config_cls 的实例，字段值从文本中提取并转换
        :raises ValueError: 如果必填字段缺失或为空，抛出异常
        """
        # 去除文本缩进
        text = textwrap.dedent(text)
        title_map = getattr(config_cls, "_title_map", {})
        required_fields = getattr(config_cls, "_required_fields", set())

        if not title_map:
            # 如果没有映射，直接返回空实例
            return config_cls()

        # 匹配所有区块标题，标题行以 section_prefix 开头
        pattern = re.compile(rf"^\s*{re.escape(section_prefix)}(.+)$", re.MULTILINE)
        matches = list(pattern.finditer(text))

        # 提取所有区块内容，存入字典，key为标题，value为内容
        all_sections = {}
        for i, match in enumerate(matches):
            title = match.group(1).strip()
            start = match.end()
            end = matches[i + 1].start() if i + 1 < len(matches) else len(text)
            content = text[start:end].strip('\n\r ')
            all_sections[title] = content

        result_kwargs = {}
        missing_required = []

        # 遍历配置类字段，映射标题内容并转换类型
        for f in fields(config_cls):
            if f.name.startswith("_"):
                # 跳过私有字段
                continue

            title = title_map.get(f.name)
            raw_value = all_sections.get(title)

            if raw_value is None or raw_value.strip() == "":
                # 缺失或空字符串
                if f.name in required_fields:
                    # name_desc = config_cls.get_field_desc(f.name)
                    missing_required.append(config_cls.get_field_desc(f.name))
                # 赋默认值或None
                if f.default is not MISSING:
                    result_kwargs[f.name] = f.default
                elif f.default_factory is not MISSING:  # type: ignore
                    result_kwargs[f.name] = f.default_factory()  # type: ignore
                else:
                    result_kwargs[f.name] = None
            else:
                # 转换字段类型
                converted = TextParseUtils._convert_value(raw_value, f.type, f.name)
                result_kwargs[f.name] = converted

        if missing_required:
            # raise ValueError(f"缺少必填字段: {missing_required}")
            raise_value_error(ErrorCode.MISSING_REQUIRED_FIELD, message=f"缺少必填字段: {missing_required}")

        # 返回配置类实例
        return config_cls(**result_kwargs)

    @staticmethod
    def extract_code_block(code_block: str, strip_newlines: bool = False) -> Optional[str]:
        """
        从只包含一个 ```xxx ... ``` 格式代码块的文本中提取代码块内容。

        :param code_block: 包含代码块的文本
        :param strip_newlines: 是否删除代码块内容中的回车换行符，默认 False
        :return: 代码块内容字符串
        :raises ValueError: 未找到代码块时抛出异常
        """
        pattern = re.compile(
            r"```[^\n]*\n(.*?)```",
            re.DOTALL
        )
        match = pattern.search(code_block)
        if match:
            content = match.group(1)
            if strip_newlines:
                # 删除所有回车换行符
                content = content.replace('\n', '').replace('\r', '')
            else:
                # 默认去除末尾多余的换行符
                content = content.rstrip('\n')
            return content
        else:
            # raise ValueError("未找到代码块")
            raise_value_error(ErrorCode.NO_CODE_BLOCK, message="解析格式失败。内容以```开头，但未找到代码块")

    @staticmethod
    def parse_json_lines(text: str) -> List[Dict]:
        """
        逐行解析JSON字符串，返回字典列表。

        :param text: 多行JSON字符串，每行一个JSON对象
        :return: 解析成功的字典列表
        """
        results = []
        for line in text.strip().splitlines():
            try:
                obj = json.loads(line)
                results.append(obj)
            except json.JSONDecodeError as e:
                print(f"解析失败: {e}，内容: {line}")
        return results

    @staticmethod
    def get_field_by_scene(items: List[Dict], target_scene: str, field: str):
        """
        从字典列表中查找指定场景的字段值。

        :param items: 字典列表，每个字典应包含 'scene' 键
        :param target_scene: 目标场景名称
        :param field: 需要获取的字段名
        :return: 找到的字段值，未找到返回 None
        """
        for item in items:
            if item.get('scene') == target_scene:
                return item.get(field)
        return None


@dataclass
class BaseConfig:
    """
    配置基类，提供统一的 to_dict 和 to_json 方法。
    """

    def to_dict(self) -> Dict[str, Any]:
        """
        将 dataclass 实例转换成字典，忽略以 _ 开头的字段。
        """
        return {
            f.name: getattr(self, f.name)
            for f in fields(self)
            if not f.name.startswith("_")
        }

    def to_json(self, **json_kwargs) -> str:
        """
        转成 JSON 字符串，默认 indent=2 方便阅读。
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2, **json_kwargs)
    
    @classmethod
    def get_field_desc(cls, field_name: str) -> str:
        """
        获取字段描述，默认从子类的 _title_map 中查找。
        如果没有找到，返回字段名本身。
        """
        title_map = getattr(cls, "_title_map", {})
        return title_map.get(field_name, field_name)

@dataclass
class LogAnalyzeConfig(BaseConfig):
    """
    日志分析配置
    """
    log_link: str  # 日志链接
    scene: str  # 场景
    is_rewrite_prompt: str  # 是否需要改写prompt
    bug_time: Optional[str] = None  # bug时间
    query: Optional[str] = None  # 用户问题
    filter_tag: List[str] = field(default_factory=list)  # 过滤tag
    delete_logs: List[Tuple[str, str]] = field(default_factory=list)  # 删除的日志行

    _required_fields: ClassVar[Set[str]] = frozenset({"log_link"})
    _title_map: ClassVar[Dict[str, str]] = {
        "log_link": "日志链接",
        "bug_time": "bug时间",
        "query": "用户问题",
        "scene": "场景",
        "is_rewrite_prompt": "是否需要改写prompt",
        "filter_tag": "过滤tag",
        "delete_logs": "删除的日志行"
    }

@dataclass
class LogSceneConfig(BaseConfig):
    """
    日志场景配置
    """
    scene: str  # 场景名称
    prompt: Optional[str] = None  # prompt
    desc: Optional[str] = None  # 场景描述
    filter_tag: List[str] = field(default_factory=list)  # 过滤tag
    index_keys: Set[str] = field(default_factory=set)  # 索引键
    save_logs: List[Tuple[str, str]] = field(default_factory=list)  # 保存的日志行
    delete_logs: List[Tuple[str, str]] = field(default_factory=list)  # 删除的日志行
    split_logs: List[Tuple[str, str]] = field(default_factory=list)  # 分割的日志行
    dedup_logs: List[Tuple[str, str]] = field(default_factory=list)  # 去重的日志行
    extract_fields: Dict = field(default_factory=dict)  # 格式化日志指定内容（转化json）
    is_fuzzy_match_tag: bool = False  # 是否模糊匹配tag
    is_analyze_daemon: bool = False  # 是否分析守护进程
    tag_ignore_patterns: Dict[str, List[str]] = field(default_factory=dict)  # 需要忽略的内容（用于去重）
    placeholder: str = ""  # 忽略占位符
    is_analyze_user_action: bool = False  # 是否分析用户行为
    is_analyze_all_logs: bool = False  # 是否分析所有日志

    _required_fields: ClassVar[Set[str]] = frozenset()
    _title_map: ClassVar[Dict[str, str]] = {
        "scene": "场景",
        "desc": "描述",
        "filter_tag": "过滤tag",
        "index_keys": "RAG索引键",
        "save_logs": "保存的日志行",
        "delete_logs": "删除的日志行",
        "split_logs": "分割的日志行",
        "dedup_logs": "去重的日志行",
        "extract_fields": "日志指定内容转化为json",
        "prompt": "prompt",
        "is_fuzzy_match_tag": "是否模糊匹配tag",
        "is_analyze_daemon": "是否分析守护进程",
        "tag_ignore_patterns": "需要忽略的内容（用于去重）",
        "placeholder": "忽略占位符",
        "is_analyze_user_action": "是否分析用户行为",
        "is_analyze_all_logs": "是否分析所有日志"
    }

    def format_all_fields(self) -> str:
        """
        返回所有字段及其描述和值的格式化字符串，方便直接打印
        """
        lines = []
        for f in fields(self):
            name = f.name
            desc = self._title_map.get(name, name)
            value = getattr(self, name)
            lines.append(f">>>> {desc}:\n{value}")
        return "\n".join(lines)


@dataclass
class AiLogConfig(BaseConfig):
    """
    AI日志配置
    """
    log_download_link: str  # 日志下载链接
    scene: str  # 场景名称
    iwiki_url: Optional[str] = None  # 描述日志分析知识的iwiki链接
    filter_tag: List[str] = field(default_factory=list)  # 过滤tag
    prompt: Optional[str] = None  # prompt
    bug_time: Optional[str] = None  # bug时间
    query: Optional[str] = None  # 用户问题
    desc: Optional[str] = None  # 场景描述
    index_keys: Set[str] = field(default_factory=set)  # 索引键
    save_logs: List[Tuple[str, str]] = field(default_factory=list)  # 保存的日志行
    delete_logs: List[Tuple[str, str]] = field(default_factory=list)  # 删除的日志行
    split_logs: List[Tuple[str, str]] = field(default_factory=list)  # 分割的日志行
    dedup_logs: List[Tuple[str, str]] = field(default_factory=list)  # 去重的日志行
    extract_fields: Dict = field(default_factory=dict)  # 格式化日志指定内容（转化json）
    is_fuzzy_match_tag: bool = False  # 是否模糊匹配tag
    is_analyze_daemon: bool = False  # 是否分析守护进程

    _required_fields: ClassVar[Set[str]] = frozenset({"log_download_link"})
    _title_map: ClassVar[Dict[str, str]] = {
        "log_download_link": "日志链接",
        "scene": "场景",
        "iwiki_url": "描述日志分析知识的iwiki链接",
        "filter_tag": "过滤tag",
        "prompt": "prompt",
        "bug_time": "bug时间",
        "query": "用户问题",
        "desc": "描述",  
        "index_keys": "索引键",
        "save_logs": "保存的日志行",
        "delete_logs": "删除的日志行",
        "split_logs": "分割的日志行",
        "dedup_logs": "去重的日志行",
        "extract_fields": "日志指定内容转化为json",
        "is_fuzzy_match_tag": "是否模糊匹配tag",
        "is_analyze_daemon": "是否分析守护进程"
    }


@dataclass
class EvaluateConfig(BaseConfig):
    """
    评价配置
    """
    ticket_id: str  # 工单ID
    evaluate: str = ''  # 满意度
    is_send_analyze_process: bool = False  # 是否需要查看分析过程

    _required_fields: ClassVar[Set[str]] = frozenset({"ticket_id"})
    _title_map: ClassVar[Dict[str, str]] = {
        "ticket_id": "工单ID",
        "evaluate": "满意度",
        "is_send_analyze_process": "是否需要查看分析过程"
    }

@dataclass
class GrayDataAnalysisConfig(BaseConfig):
    """
    灰度分析用户输入配置
    """
    version: str  # 版本号 如 班车0608、900
    # 是直接捞取数据还是查看文档
    is_fetch_data: str = '0'  # 0: 查看文档 1: 直接捞取数据

    _required_fields: ClassVar[Set[str]] = frozenset({"version"})
    _title_map: ClassVar[Dict[str, str]] = {
        "version": "版本号",
        "is_fetch_data": "数据获取方式选择" 
    }



# if __name__ == '__main__':
# 测试需导入iwiki_client包，但iwiki_client导入了本包，避免循环导入，请在iwiki_client中测试
    # iwiki_client = IWikiClient()
    # iwiki_data = iwiki_client.get_doc_body("https://iwiki.woa.com/p/4014612546")
    
    # parse_iwiki_content = TextParseUtils.parse(
    #                 text=iwiki_data,
    #                 config_cls=LogSceneConfig,
    #                 section_prefix=">>> "
    #             )
    # print(parse_iwiki_content)
    # print(f'save_logs={parse_iwiki_content.save_logs}, type={type(parse_iwiki_content.save_logs)}')

    