import requests
import json
from datetime import datetime
from typing import List, Dict, Optional
from common.error.detailed_value_error import raise_value_error, ErrorCode, DetailedValueError


class TediClient:
    """
    Tedi API，用于获取数据。
    调用文档：https://tedi.woa.com/tediweb/digital-process/instance-detail?workspaceId=92&processId=7ec0cf75a8dcd13248e82489db5d44b6&workFlowId=a6f042f46c93a6d293a316b0b4517730&flowCategory=CLCT
    """

    BASE_URL = "http://api.apigw.oa.com/tedi-api/api/clct"
    VERSION_LIST_PATH = "/version/list"
    VERSION_DETAIL_PATH = "/version/detail"
    VERSION_RUNNING_PATH = "/version/running"

    def __init__(self):
        self.session = requests.Session()

    def _find_version_workflow_id(self, version_list: List[Dict], version: str) -> Optional[str]:
        """
        在版本列表中查找指定版本号对应的workflowId

        Args:
            version_list: 版本信息列表
            version: 版本号，如 "899"

        Returns:
            Optional[str]: workflowId，如果未找到返回None
        """
        for version_info in version_list:
            # 从appVersion中提取数字版本号（去掉可能的点号）
            app_version = version_info.get('appVersion', '')
            version_num = ''.join(filter(str.isdigit, app_version))
            
            if version_num == version:
                return version_info.get('id')
        return None

    def get_version_list(self, start_time: Optional[str] = None, end_time: Optional[str] = None, project_id: str = "92", project_name: str = "应用宝") -> List[Dict]:
        """
        获取所有版本信息列表

        Args:
            start_time: 开始时间，格式为 "YYYY-MM-DD HH:mm:ss"，可选，默认为 "2024-01-01 00:00:00"
            end_time: 结束时间，格式为 "YYYY-MM-DD HH:mm:ss"，可选，默认为当前时间后一年
            project_id: 项目ID，默认为"92"
            project_name: 项目名称，默认为"应用宝"

        Returns:
            List[Dict]: 版本信息列表

        Raises:
            DetailedValueError: 
                - 当时间格式无效时
                - 当API调用失败时
        """
        # 设置默认时间范围
        if not start_time:
            start_time = "2024-01-01 00:00:00"
        if not end_time:
            end_time = (datetime.now().replace(year=datetime.now().year + 1)).strftime("%Y-%m-%d %H:%M:%S")

        try:
            # 验证时间格式
            datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise_value_error(
                ErrorCode.INVALID_PARAMETER,
                message="时间格式无效，应为 YYYY-MM-DD HH:mm:ss",
                context={"start_time": start_time, "end_time": end_time}
            )

        # 构建请求参数
        params = {
            "projectId": project_id,
            "projectName": project_name,
            "startTime": start_time,
            "endTime": end_time
        }

        try:
            # 发送请求
            response = self.session.get(
                f"{self.BASE_URL}{self.VERSION_LIST_PATH}",
                params={"param": json.dumps(params)},
                timeout=10
            )
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") != 0:
                raise_value_error(
                    ErrorCode.TEDI_API_ERROR,
                    message=f"获取版本列表失败: {result.get('message', '未知错误')}"
                )

            return result.get("data", [])

        except Exception as e:
            raise_value_error(
                ErrorCode.TEDI_API_ERROR,
                message=f"获取版本列表失败: {str(e)}"
            )

    def _process_workflow_data(self, workflow_data: Dict) -> Dict:
        """
        处理流程详情数据，提取关键信息
        
        Args:
            workflow_data: 原始流程详情数据
            
        Returns:
            Dict: 处理后的简化数据，包含stages和steps的关键信息
        """
        def _extract_time_range(item: Dict) -> Dict:
            """提取时间范围信息"""
            status = item.get('status', '').upper()
            if status == 'WAITING':
                time_range = item.get('planTimeRange', {}) or {}
            elif status == 'FINISH':
                time_range = item.get('actualTimeRange', {}) or {}
            else:
                time_range = {}
            
            return {
                'startTime': time_range.get('startTime'),
                'endTime': time_range.get('endTime')
            }
        
        def _process_steps(steps: List[Dict]) -> List[Dict]:
            """处理steps列表"""
            if not steps:
                return []
            
            processed_steps = []
            for step in steps:
                time_info = _extract_time_range(step)
                processed_step = {
                    'name': step.get('name'),
                    'startTime': time_info['startTime'],
                    'endTime': time_info['endTime'],
                    'status': step.get('status', '').upper()
                }
                
                # 提取负责人operations字段
                if step.get('operations'):
                    processed_step['operations'] = step['operations']
                
                # 处理nextSteps
                if step.get('nextSteps'):
                    processed_step['nextSteps'] = _process_steps(step['nextSteps'])
                
                processed_steps.append(processed_step)
            
            return processed_steps
        
        def _process_stages(stages: List[Dict]) -> List[Dict]:
            """处理stages列表"""
            if not stages:
                return []
            
            processed_stages = []
            for stage in stages:
                time_info = _extract_time_range(stage)
                processed_stage = {
                    'name': stage.get('name'),
                    'startTime': time_info['startTime'],
                    'endTime': time_info['endTime'],
                    'status': stage.get('status', '').upper()
                }
                
                # 处理steps
                if stage.get('steps'):
                    processed_stage['steps'] = _process_steps(stage['steps'])
                
                # 处理nextStages
                if stage.get('nextStages'):
                    processed_stage['nextStages'] = _process_stages(stage['nextStages'])
                
                processed_stages.append(processed_stage)
            
            return processed_stages
        

        process_id = workflow_data.get('processId')
        if process_id:
            website_url = f"https://tedi.woa.com/tediweb/digital-process/process-detail?workspaceId=92&processId={process_id}&detailTab=flow"
        else:
            website_url = "构建失败"

        # 处理主stages
        processed_data = {
            'id': workflow_data.get('id'),
            'projectName': workflow_data.get('projectName'),
            'status': workflow_data.get('status'),
            'website_url': website_url,
            'stages': _process_stages(workflow_data.get('stages', []))
        }
        
        return processed_data

    def get_version_workflow_detail(self, version: str) -> tuple[Dict, Dict]:
        """
        获取指定版本的流程详情

        Args:
            version: 版本号，如 "899"

        Returns:
            tuple[Dict, Dict]: (raw_workflow_detail, processed_workflow_detail)
                - raw_workflow_detail: 原始流程详情数据
                - processed_workflow_detail: 处理后的流程详情数据

        Raises:
            DetailedValueError: 当版本号为空或获取失败时抛出
        """
        # 参数验证
        if not version or not version.strip():
            raise_value_error(
                ErrorCode.MISSING_REQUIRED_FIELD,
                message="版本号不能为空"
            )

        # 获取版本列表（使用默认时间范围）
        version_list = self.get_version_list()
        
        # 查找对应版本的workflowId
        workflow_id = self._find_version_workflow_id(version_list, version)
        if not workflow_id:
            raise_value_error(
                ErrorCode.TEDI_API_ERROR,
                message=f"未找到版本 {version} 的流程信息"
            )

        try:
            # 构建参数
            params = {"workflowId": workflow_id}
            url = f"{self.BASE_URL}{self.VERSION_DETAIL_PATH}?param={json.dumps(params)}"
            
            # 发送请求
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            result = response.json()

            if result.get("code") != 0:
                raise_value_error(
                    ErrorCode.TEDI_API_ERROR,
                    message=f"获取流程详情失败: {result.get('message', '未知错误')}"
                )

            raw_workflow_detail = result.get("data")
            if not raw_workflow_detail:
                raise_value_error(
                    ErrorCode.TEDI_API_ERROR,
                    message="未获取到流程详情数据"
                )

            # 处理数据
            processed_workflow_detail = self._process_workflow_data(raw_workflow_detail)

            return raw_workflow_detail, processed_workflow_detail

        except Exception as e:
            raise_value_error(
                ErrorCode.TEDI_API_ERROR,
                message=f"获取流程详情失败: {str(e)}"
            )

    def get_stageOrStep_info(
        self,
        version: str,
        stage_name: Optional[str] = None,
        step_name: Optional[str] = None,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None
    ) -> Dict:
        """
        根据stage名或step名查询对应的信息。
        至少需要提供stage_name或step_name中的一个。

        Args:
            version: 版本号，如 "899"
            stage_name: stage名称（支持部分匹配），可选
            step_name: step名称（支持部分匹配），可选
            start_time: 开始时间，格式为 "YYYY-MM-DD HH:mm:ss"，可选（已废弃，保留参数兼容性）
            end_time: 结束时间，格式为 "YYYY-MM-DD HH:mm:ss"，可选（已废弃，保留参数兼容性）

        Returns:
            Dict: 
                - 查询stage：{'stage': {...}}
                - 查询step：{'step': {...}}

        Raises:
            DetailedValueError: 
                - 当版本号为空时
                - 当stage_name和step_name都未提供时
                - 当未找到指定的stage时
                - 当未找到指定的step时
                - 当获取流程详情失败时
        """
        # 参数检查
        if not version or not version.strip():
            raise_value_error(
                ErrorCode.MISSING_REQUIRED_FIELD,
                message="版本号不能为空"
            )
        if not stage_name and not step_name:
            raise_value_error(
                ErrorCode.MISSING_REQUIRED_FIELD,
                message="stage_name和step_name至少需要提供一个"
            )

        # 获取流程详情
        try:
            raw_data, processed_data = self.get_version_workflow_detail(version)
        except DetailedValueError as e:
            raise_value_error(
                ErrorCode.TEDI_API_ERROR,
                message=f"获取版本 {version} 的流程详情失败: {e.message}",
                context={"version": version}
            )

        def _find_stage(stage_list, stage_name):
            if not stage_list:
                return None
            for s in stage_list:
                if stage_name and stage_name.lower() in s.get('name', '').lower():
                    return s
                # 递归查找 nextStages
                found = _find_stage(s.get('nextStages', []), stage_name)
                if found:
                    return found
            return None

        def _find_step(stage_list, step_name):
            if not stage_list:
                return None, None
            
            for stage in stage_list:
                # 在当前stage的steps中查找
                for step in stage.get('steps', []):
                    if step_name and step_name.lower() in step.get('name', '').lower():
                        return stage, step
                    
                    # 递归查找nextSteps
                    def _search_next_steps(steps, target_name):
                        if not steps:
                            return None
                        for s in steps:
                            if target_name and target_name.lower() in s.get('name', '').lower():
                                return s
                            found = _search_next_steps(s.get('nextSteps', []), target_name)
                            if found:
                                return found
                        return None
                    
                    found_step = _search_next_steps(step.get('nextSteps', []), step_name)
                    if found_step:
                        return stage, found_step
                
                # 递归查找nextStages
                found_stage, found_step = _find_step(stage.get('nextStages', []), step_name)
                if found_step:
                    return found_stage, found_step
            
            return None, None
        
        result = {}
        stage = None
        step = None
        
        # 1. 处理stage
        if stage_name:
            # 如果提供了stage_name，搜索指定的stage
            stage = _find_stage(processed_data.get('stages', []), stage_name)
            if not stage:
                stage = _find_stage(processed_data.get('nextStages', []), stage_name)
            if not stage:
                raise_value_error(
                    ErrorCode.TEDI_API_ERROR,
                    message=f"未找到包含 '{stage_name}' 的stage",
                    context={"stage_name": stage_name}
                )
            
            # 获取stage信息（处理后数据已经包含正确的起止时间）
            result['stage'] = {
                'name': stage.get('name'),
                'startTime': stage.get('startTime'),
                'endTime': stage.get('endTime'),
                'status': stage.get('status')
            }

        # 2. 处理step
        if step_name:
            # 搜索指定的step
            found_stage, found_step = _find_step(processed_data.get('stages', []), step_name)
            if not found_step:
                found_stage, found_step = _find_step(processed_data.get('nextStages', []), step_name)
            if not found_step:
                raise_value_error(
                    ErrorCode.TEDI_API_ERROR,
                    message=f"未找到包含 '{step_name}' 的step",
                    context={"step_name": step_name}
                )
            
            # 获取step信息（处理后数据已经包含正确的起止时间）
            result['step'] = {
                'name': found_step.get('name'),
                'startTime': found_step.get('startTime'),
                'endTime': found_step.get('endTime'),
                'status': found_step.get('status')
            }

        return result

    def get_running_versions_info(self, project_name: str = "应用宝", platform: str = "ANDROID") -> List[Dict]:
        """
        获取正在运行的版本列表，并提取关键信息。

        Args:
            project_name: 项目名称，默认为"应用宝"
            platform: 平台，默认为"ANDROID"

        Returns:
            List[Dict]: 包含版本信息的列表，每个元素包含：
                - id: 版本ID
                - appVersion: 版本号
                - 合流截止: 合流截止时间
                - 一灰: 一灰时间
                - 全量: 全量时间
                - currentStage: 当前stage名称
                - currentStep: 当前step名称
                - currentOperators: 当前步骤的负责人信息

        Raises:
            DetailedValueError: 当API调用失败时
        """
        try:
            # 构建请求参数
            params = {
                "projectName": project_name,
                "platform": platform
            }
            
            # 发送请求
            response = self.session.get(
                f"{self.BASE_URL}{self.VERSION_RUNNING_PATH}",
                params={"param": json.dumps(params)},
                timeout=10
            )
            response.raise_for_status()
            result = response.json()

            if result.get("code") != 0:
                raise_value_error(
                    ErrorCode.TEDI_API_ERROR,
                    message=f"获取运行版本列表失败: {result.get('message', '未知错误')}"
                )

            # 处理数据
            version_list = result.get("data", [])
            processed_list = []
            
            for version in version_list:
                # 提取基本信息
                version_info = {
                    "id": version.get("id"),
                    "appVersion": version.get("appVersion")
                }
                
                # 添加里程碑时间作为顶层字段
                milestone_times = version.get("mileStoneStartTime", {})
                version_info.update(milestone_times)
                
                # 提取当前节点信息
                current_nodes = version.get("currentNodes", [])
                if current_nodes:
                    # 获取最后一个节点的信息（最新的状态）
                    latest_node = current_nodes[-1]
                    version_info.update({
                        "currentStage": latest_node.get("stageName"),
                        "currentStep": latest_node.get("stepName")
                    })
                    
                    # 查找当前步骤的负责人信息
                    current_step_id = latest_node.get("stepId")
                    current_operators = self._find_step_operators(version.get("stages", []), current_step_id)
                    if current_operators:
                        version_info["currentOperators"] = current_operators
                
                processed_list.append(version_info)

            return processed_list

        except Exception as e:
            raise_value_error(
                ErrorCode.TEDI_API_ERROR,
                message=f"获取运行版本列表失败: {str(e)}"
            )

    def _find_step_operators(self, stages: List[Dict], target_step_id: str) -> List[Dict]:
        """
        在stages中查找指定stepId的operations信息
        
        Args:
            stages: 阶段列表
            target_step_id: 目标步骤ID
            
        Returns:
            List[Dict]: 操作者信息列表
        """
        def search_in_stages(stage_list):
            if not stage_list:
                return None
            
            for stage in stage_list:
                # 在stage的steps中查找
                if stage.get('steps'):
                    result = search_in_steps(stage['steps'])
                    if result:
                        return result
                
                # 递归查找nextStages
                if stage.get('nextStages'):
                    result = search_in_stages(stage['nextStages'])
                    if result:
                        return result
            
            return None
        
        def search_in_steps(step_list):
            if not step_list:
                return None
            
            for step in step_list:
                # 检查当前step
                if step.get('id') == target_step_id and step.get('operations'):
                    return step['operations']
                
                # 递归查找nextSteps
                if step.get('nextSteps'):
                    result = search_in_steps(step['nextSteps'])
                    if result:
                        return result
            
            return None
        
        return search_in_stages(stages) or []

    
if __name__ == "__main__":
    client = TediClient()
    
    print("=== 测试 format_current_version_plan 和负责人信息 ===")

    # 获取正在运行的版本信息
    running_versions = client.get_running_versions_info()
    print(running_versions)
        
        
    
    
   

