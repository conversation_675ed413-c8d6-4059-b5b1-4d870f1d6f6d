"""
工蜂 (Tencent Git) API 操作客户端
提供仓库对比、合并请求操作等 Git 相关功能
参考文档：https://git.woa.com/help/menu/api/repositorys.html
"""

import requests
import re
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from common.client.tapd_client import TapdClient


class GitClientError(Exception):
    """Git 客户端操作异常"""
    pass


class GitClient:
    """
    工蜂 (Tencent Git) API 交互客户端

    支持的操作包括：
    - 仓库对比（分支、提交、标签）
    - 合并请求操作
    - 文件变更跟踪
    - 获取mr对应的tapd单子
    """

    def __init__(self, project_id: str, private_token: str, base_url: str = "https://git.woa.com",
                 project_path: str = "yyb-android/TencentMobileAssistant"):
        """
        初始化 Git 客户端

        Args:
            project_id: Git 项目 ID
            private_token: 认证私钥
            base_url: Git API 基础 URL（默认：https://git.woa.com）
            project_path: 项目的全路径（默认：yyb-android/TencentMobileAssistant）
        """
        self.project_id = project_id
        self.private_token = private_token
        self.base_url = base_url.rstrip('/')
        self.project_path = project_path
        self.api_base = f"{self.base_url}/api/v3/projects/{self.project_id}"
        self.tapd_client = TapdClient()

    def _make_request(self, endpoint: str, params: Optional[Dict] = None, method: str = "GET") -> Dict[str, Any]:
        """
        向 Git API 发送 HTTP 请求

        Args:
            endpoint: API 端点（相对于项目基础路径）
            params: 查询参数
            method: HTTP 方法

        Returns:
            JSON 响应数据

        Raises:
            GitClientError: 请求失败时抛出
        """
        url = f"{self.api_base}/{endpoint.lstrip('/')}"

        # print(f'url: {url}')
        # print(f'params: {params}')

        if params is None:
            params = {}

        # 添加认证令牌
        params['private_token'] = self.private_token

        try:
            response = requests.request(method, url, params=params)

            if response.status_code != 200:
                raise GitClientError(
                    f"API 请求失败，状态码 {response.status_code}: {response.text}"
                )

            return response.json()

        except requests.RequestException as e:
            raise GitClientError(f"请求失败: {str(e)}")
        
    def get_all_commits(self, ref_name: str = 'master', page: int = 1, per_page: int = 100) -> List[Dict[str, Any]]:
        """
        获取所有提交记录

        API: GET /api/v3/projects/:id/repository/commits
        文档：https://git.woa.com/help/menu/api/commits.html#%E5%88%97%E5%87%BA%E9%A1%B9%E7%9B%AE%E7%89%88%E6%9C%AC%E5%BA%93%E6%89%80%E6%9C%89%E7%9A%84%E6%8F%90%E4%BA%A4

        Args:
            ref_name: 版本库分支 或 tag，默认：master
            page: 页码（默认：1）
            per_page: 每页结果数量（默认：100）

        Returns:
            提交记录列表
        """
        params = {
            'ref_name': ref_name,
            'page': page,
            'per_page': per_page
        }

        return self._make_request('repository/commits', params)
        
    def get_commit_branches(self, sha: str, ) -> List[str]:
        """
        获取指定 commit 的所有分支名称

        API: GET /api/v3/projects/:id/repository/commits/:sha/refs
        文档：https://git.woa.com/help/menu/api/commits.html#%E8%8E%B7%E5%8F%96%E6%9F%90%E4%B8%AA%E6%8F%90%E4%BA%A4%E5%AF%B9%E5%BA%94%E7%9A%84%E5%88%86%E6%94%AF%E5%92%8C-tag
        
        Args:
            sha: commit hash 值、分支名或 tag

        Returns:
            包含分支名称的列表
        """
        params = {
            # "type": "branch"
            # "per_page": 30
        }
        response = self._make_request(f'repository/commits/{sha}/refs', params)

        return [branch['name'] for branch in response]
        
    def get_branch_info(self, branch_name: str) -> List[Dict[str, Any]]:
        """
        获取分支信息

        API: GET /api/v3/projects/:id/tloc/branch/lifecycle
        文档：https://git.woa.com/help/menu/api/branches.html#%E6%9F%A5%E8%AF%A2%E5%88%86%E6%94%AF%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F

        Args:
            branch_name: 分支名称

        Returns:
            包含分支信息的字典
        """
        params = {
            'branch_name': branch_name
        }
        return self._make_request(f'tloc/branch/lifecycle', params)

    def compare_branches(self, from_ref: str, to_ref: str, straight: bool = True) -> Dict[str, Any]:
        """
        获取两个分支、提交或标签之间的差异内容
        参考文档：https://git.woa.com/help/menu/api/repositorys.html#%E8%8E%B7%E5%8F%96%E5%B7%AE%E5%BC%82%E5%86%85%E5%AE%B9

        API: GET /api/v3/projects/:id/repository/compare

        Args:
            from_ref: 目标分支：提交的 hash、分支 或 tag，不传时表示比较to与其第一 parent
            to_ref: 源分支：提交的 hash、分支 或 tag
            straight: true：两个点比较差异，false：三个点比较差异。文档：https://km.woa.com/articles/show/539700?kmref=author_recommend 

        Returns:
            包含对比数据的字典，结构如下：
            {
                "commit": {...},
                "base_commit": {...},
                "commits": [...],
                "diffs": [...],
                "compare_timeout": bool,
                "compare_same_ref": bool,
                "over_flow": bool,
                "files_total": int,
                "commits_total": int,
                "ahead_by": int,
                "behind_by": int
            }
        """
        params = {
            'from': from_ref,
            'to': to_ref,
            'straight': straight
        }
        print(f'compare params: ',params)

        return self._make_request('repository/compare', params)
    
    def compare_branches_commit(self, from_ref: str, to_ref: str):
        """
        获取两个分支之间的差异 commits
        """
        params = {
            'from': from_ref,
            'to': to_ref
        }

        return self._make_request('repository/compare/commits', params)


    def get_merge_request(self, merge_request_id: int) -> Dict[str, Any]:
        """
        获取指定合并请求的详细信息

        API: GET /api/v3/projects/:id/merge_request/:merge_request_id

        Args:
            merge_request_id: 合并请求的 ID

        Returns:
            包含合并请求详细信息的字典
        """
        return self._make_request(f'merge_request/{merge_request_id}')

    def get_merge_requests(self,
                          state: str = 'merged',
                          target_branch: str = 'master',
                          order_by: str = 'resolve_at',
                          per_page: int = 200,
                          created_after: Optional[str] = None,
                          iids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        获取符合条件的合并请求列表

        API: GET /api/v3/projects/:id/merge_requests

        Args:
            state: 合并请求状态（默认：'merged'）
            target_branch: 目标分支过滤（默认：'master'）
            order_by: 排序字段（默认：'resolve_at'）
            per_page: 每页结果数量（默认：200）
            created_after: 过滤此日期后创建的 MR（ISO 格式）
            iids: 要过滤的 MR IID 列表

        Returns:
            合并请求字典列表
        """
        params = {
            'state': state,
            'target_branch': target_branch,
            'order_by': order_by,
            'per_page': per_page
        }

        if created_after:
            params['created_after'] = created_after

        if iids:
            params['iids[]'] = iids

        return self._make_request('merge_requests', params)

    def get_merge_request_changes(self, merge_request_id: int) -> Dict[str, Any]:
        """
        获取合并请求中的文件变更信息

        API: GET /api/v3/projects/:id/merge_request/:merge_request_id/changes

        Args:
            merge_request_id: 合并请求的 ID

        Returns:
            包含文件变更和差异信息的字典
        """
        return self._make_request(f'merge_request/{merge_request_id}/changes')

    def get_tapd_workitems(self, mr_iid: str, item_type: str = 'mr') -> List[Dict[str, Any]]:
        """
        获取与合并请求关联的 TAPD 工作项

        API: GET /api/v3/projects/:id/tapd_workitems

        Args:
            mr_iid: 合并请求的 IID
            item_type: 项目类型（默认：'mr'）

        Returns:
            TAPD 工作项字典列表
        """
        params = {
            'type': item_type,
            'iid': mr_iid
        }

        return self._make_request('tapd_workitems', params)
    
    def get_mr_info_by_date(self, branch_name: str) -> List[Dict[str, Any]]:
        """
        获取 指定日期 至今 的合并请求列表

        Args:
            branch_name: 发布分支名称。根据该发布分支的创建时间作为起点

        Returns:
            合并请求字典列表
        """
        # 获取上个版本发布分支 创建的时间
        branch_info = self.get_branch_info(branch_name)
        branch_create_time = branch_info[0].get('create_date')
        mr_list = self.get_merge_requests(state='merged', 
                                target_branch='master',
                                order_by='resolve_at',
                                per_page=200,
                                created_after=branch_create_time)
        # print(f"get_mr_by_date 获取到的 第一个mr：{mr_list[0]}")

        return mr_list

    def _format_release(self, version):
        """ 将版本号转为发布分支 """
        # 把输入转成字符串
        s = str(version)
        # 用点分隔每个字符
        parts = list(s)
        # 拼接成 release- + 点分隔字符串
        return "release-" + ".".join(parts)

    def extract_mr_iids_from_commits(self, commits: List[Dict[str, Any]]) -> List[str]:
        """
        从提交标题中提取合并请求 IID

        Args:
            commits: 包含 'title' 字段的提交字典列表

        Returns:
            提取到的 MR IID 列表
        """
        # 正则表达式匹配 "merge request !" 后跟数字
        pattern = re.compile(r'merge request !(\d+)')

        extracted_numbers = []
        no_mr_id_list = []
        for commit in commits:
            title = commit.get('title', '')
            match = pattern.search(title)
            if match:
                extracted_numbers.append(match.group(1))
            else:
                no_mr_id_list.append(commit)
                print(f"No match for: {commit.get('title', '')}")
        

        # title_list = [commit['title'] for commit in commits]

        # 从提交标题中提取 MR 编号
        # extracted_numbers = [
        #     match.group(1)
        #     for match in map(pattern.search, title_list)
        #     if match
        # ]

        # for title in title_list:
        #     match = pattern.search(title)
        #     if match:
        #         extracted_numbers.append(match.group(1))
        #     else:
        #         no_mr_id_list.append(title)
        #         print(f"No match for: {title}")

        return extracted_numbers, no_mr_id_list

    def get_mr_iids_from_version_comparison(self, version: str, target: str = 'master') -> List[str]:
        """
        通过版本/分支对比获取合并请求 IID

        这是一个便捷方法，结合了分支对比和 MR IID 提取功能

        Args:
            version: 源版本分支/标签
            target: 目标分支（默认：'master'）

        Returns:
            在对比中找到的合并请求 IID 列表
        """
        comparison_result = self.compare_branches(version, target)
        commits = comparison_result.get('commits', [])
        for commit in commits:
            print(f"提交信息：{commit.get('title', '')}")
        # print(f"提交列表：{commits}")
        print(f"提交列表个数：{len(commits)}")

        # comparison_result = self.compare_branches_commit(version, target)
        # print(f"对比结果：{comparison_result}")
        # for commit in comparison_result:
        #     print(f"提交信息：{commit.get('title', '')}")

        return self.extract_mr_iids_from_commits(commits)

    def get_recent_merged_mrs_by_iids(self, mr_iids: List[str], days_back: int = 30) -> List[Dict[str, Any]]:
        """
        根据 IID 获取最近合并的 MR，并过滤插件mr

        Args:
            mr_iids: 要过滤的 MR IID 列表
            days_back: 回溯天数（默认：30）

        Returns:
            包含附加字段的合并请求字典列表
        """
        # 计算日期过滤器
        time_ago = datetime.now() - timedelta(days=days_back)
        created_after_str = time_ago.strftime('%Y-%m-%dT%H:%M:%S+0000')
        # created_after_str = created_after_str.replace('+', '%2B')
        print(f"created_after_str: {created_after_str}")
        

        # 获取 MR 列表
        mr_list = self.get_merge_requests(
            state='merged',
            target_branch='master',
            order_by='resolve_at',
            per_page=200,
            created_after=created_after_str,
            iids=mr_iids
        )
        if not mr_list:
            print("没有找到符合条件的 MR！")

        print(f"get_recent_merged_mrs_by_iids 获取到的 第一个mr：{mr_list[0]}")

        # 过滤和格式化结果
        filtered_mrs = []
        total_mrs = []
        for mr in mr_list:
            total_mrs.append({
                'iid': mr['iid'],
                'id': mr['id'],
                'author': mr['author']['name'],
                'title': mr.get('title', ''),
                'created_at': mr.get('created_at', ''),
                'merged_at': mr.get('merged_at', '')
            })
            if str(mr['iid']) in mr_iids:
                is_plugin_mr = self.is_plugin_related_mr(mr['id'])
                if is_plugin_mr:
                    print(f"这是一个与插件相关的 MR，不进行收集！MR信息：{mr.get('title', '')}")
                else:
                    filtered_mrs.append({
                        'iid': mr['iid'],
                        'id': mr['id'],
                        'author': mr['author']['name'],
                        'title': mr.get('title', ''),
                        'description': mr.get('description', ''),
                        'created_at': mr.get('created_at', ''),
                        'merged_at': mr.get('merged_at', '')
                    })
        # print(f"过滤后的 MR 总数量：{len(filtered_mrs)}, 列表：{filtered_mrs}")
        print(f"过滤后的 MR 总数量：{len(filtered_mrs)}")
        return filtered_mrs

    def extract_changed_directories_from_mr(self, merge_request_id: int) -> List[str]:
        """
        从合并请求中提取变更目录列表

        Args:
            merge_request_id: 合并请求的 ID

        Returns:
            被修改的目录路径列表
        """
        changes = self.get_merge_request_changes(merge_request_id)
        files = changes.get('files', [])

        directories = []
        # pattern = re.compile(r"diff --git a/(.+?) b/\1")

        # for file_info in files:
        #     diff_content = file_info.get('diff', '')
        #     # 在差异中查找所有目录路径
        #     directories.extend(re.findall(pattern, diff_content))
        for file_info in files:
            path = file_info.get('new_path', '')
            if path:
                directories.append(path)

        # 去重并返回
        return list(set(directories))

    def is_plugin_related_mr(self, merge_request_id: int, plugin_prefix: str = "Plugin/") -> bool:
        """
        检查合并请求是否包含插件相关变更

        Args:
            merge_request_id: 合并请求的 ID
            plugin_prefix: 识别插件目录的前缀（默认："Plugin/"）

        Returns:
            如果 MR 包含插件变更返回 True，否则返回 False
        """
        directories = self.extract_changed_directories_from_mr(merge_request_id)
        # 有一些变更目录，不止包含Plugin目录，还有其他目录，如AI相册。这里就把变更目录只包含Plugin的剔除
        return all(directory.startswith(plugin_prefix) for directory in directories)

    def build_mr_url(self, mr_iid: int) -> str:
        """
        根据 MR 的 IID 构建合并请求链接

        Args:
            mr_iid: 合并请求的 IID

        Returns:
            完整的 MR 链接 URL
        """
        return f"{self.base_url}/{self.project_path}/-/merge_requests/{mr_iid}"

    def build_mr_urls_from_iids(self, mr_iids: List[str]) -> Dict[str, str]:
        """
        批量构建 MR 链接

        Args:
            mr_iids: MR IID 列表

        Returns:
            IID 到 URL 的映射字典
        """
        return {iid: self.build_mr_url(int(iid)) for iid in mr_iids}

    def parse_mr_description(self, description: str) -> Dict[str, str]:
        """
        解析 MR 描述信息，提取关键字段

        支持的字段格式：
        【需求地址】- 必须，请在创建MR的工蜂上关联TAPD单即可
           * https://tapd.woa.com/tapd_fe/20422314/story/detail/1020422314124595139
        【实验报告地址】- 必须, 标注实验的具体地址即可
           * https://tab.woa.com/tab/experiment/exp-result?expGroupId=1540843...
        【是否预埋需求】- 必须，版本灰度期间是否生效
           * 是
        【集成测试关注点】- 必须, 集成测试阶段需要关注的功能点
           * 不涉及
        【开关说明】- 非必须， 明确开关系统、Key，取值，开关打开方式等
           * 无

        Args:
            description: MR 的描述内容

        Returns:
            包含解析字段的字典，格式如下：
            {
                'tapd_url': 'TAPD工作项链接',
                'experiment_report_url': '实验报告地址',
                'is_pre_embedded': '是否预埋需求（是/否）',
                'integration_test_focus': '集成测试关注点',
                'switch_description': '开关说明'
            }
        """
        result = {
            'tapd_url': '',
            'experiment_report_url': '',
            'is_pre_embedded': '',
            'integration_test_focus': '',
            'switch_description': ''
        }

        if not description:
            return result

        # 定义字段映射和对应的正则表达式
        field_patterns = {
            'tapd_url': {
                'pattern': r'【需求地址】[^*]*\*\s*(https://tapd\.woa\.com[^\s\n]*)',
                'fallback_pattern': r'(https://tapd\.woa\.com[^\s\n]*)'
            },
            'experiment_report_url': {
                'pattern': r'【实验报告地址】[^\n]*\n(?:\*\s*)?([^\n]+)',
                'fallback_pattern': r'(https://tab\.woa\.com[^\s\n]*)'
            },
            'is_pre_embedded': {
                'pattern': r'【是否预埋需求】[^\n]*\n(?:\*\s*)?([^\n]+)',
                'fallback_pattern': None
            },
            'integration_test_focus': {
                'pattern': r'【集成测试关注点】[^\n]*\n(?:\*\s*)?([^\n]+)',
                'fallback_pattern': None
            },
            'switch_description': {
                'pattern': r'【开关说明】[^\n]*\n(?:\*\s*)?([^\n]+)',
                'fallback_pattern': None
            }
        }

        # 逐个提取字段
        for field_key, patterns in field_patterns.items():
            # 尝试主要模式
            match = re.search(patterns['pattern'], description, re.DOTALL)
            if match:
                result[field_key] = match.group(1).strip().replace('*', '')
            elif patterns.get('fallback_pattern'):
                # 尝试备用模式
                fallback_match = re.search(patterns['fallback_pattern'], description)
                if fallback_match:
                    result[field_key] = fallback_match.group(1).strip()

        return result
    
    def get_mr_list_details_by_date(self, version: str) -> List[Dict]:
        """
        根据日期获取 MR 列表，并解析每个 MR 的详细信息

        Args:
            version: 版本号

        Returns:
            包含详细信息的 MR 列表，每个 MR 包含以下字段：
            [
                {
                    'iid': MR的IID,
                    'id': MR的ID,
                    'title': MR标题（需求）,
                    'creator': 需求创建人
                    'type': 需求类型
                    'author': MR作者（终端开发）,
                    'mr_url': MR链接,
                    'tapd_url': TAPD工作项链接（需求单）,
                    'tapd_workitems': TAPD工作项详细信息列表,
                    'is_pre_embedded': 是否预埋（是/否）,
                    'integration_test_focus': 测试关注重点,
                    'switch_description': 开关说明,
                    'experiment_report_url': 实验报告地址,
                    'created_at': 创建时间,
                    'merged_at': 合并时间
                }
            ]
        """
        branch_name  = self._format_release(int(version)-1)
        print(f"获取 {branch_name} 分支的创建时间 至今的 MR列表 ")
        mr_info_list = self.get_mr_info_by_date(branch_name)

        filtered_mrs = []
        for mr in mr_info_list:
            is_plugin_mr = self.is_plugin_related_mr(mr['id'])
            if is_plugin_mr:
                print(f"这是一个与插件相关的 MR，不进行收集！MR信息：{mr.get('title', '')}")
            else:
                filtered_mrs.append(mr)

        print(f"过滤后的 MR 总数量：{len(filtered_mrs)}")

        # 批量获取 TAPD 工作项信息
        print(f"批量获取 TAPD 工作项信息")
        tapd_workitems_batch = self.get_tapd_workitems_batch([str(mr_info['iid']) for mr_info in filtered_mrs])


        detailed_mrs = []
        for mr_info in filtered_mrs:
            try:
                mr_iid_str = str(mr_info['iid'])
                # 解析mr描述
                parsed_desc = self.parse_mr_description(mr_info.get('description', ''))
                # 构建mr链接
                mr_url = self.build_mr_url(mr_info['iid'])
                # 获取tapd工作项信息
                tapd_workitems = tapd_workitems_batch.get(mr_iid_str, [])

                tapd_url = ''
                if tapd_workitems:
                    tapd_urls, tapd_info = self.build_tapd_urls_from_workitems(tapd_workitems)
                    if tapd_urls:
                        # 拼接所有 TAPD 链接作为 需求单
                        tapd_url = "\n".join(tapd_urls.values())

                # 组装详细信息
                detailed_mr = {
                    'iid': mr_info['iid'],
                    'id': mr_info['id'],
                    'title': mr_info.get('title', ''),  # 需求
                    'creator': tapd_info.get('creator', ''),  # 需求创建人
                    'type': tapd_info.get('type', ''),  # 需求类型
                    'author': mr_info.get('author', {}).get('name', ''),  # 终端开发
                    'mr_url': mr_url,  # MR链接
                    'tapd_url': tapd_url,  # 需求单
                    'tapd_workitems': tapd_workitems,  # TAPD 工作项详细信息
                    'is_pre_embedded': parsed_desc.get('is_pre_embedded', ''),  # 是否预埋
                    'integration_test_focus': parsed_desc.get('integration_test_focus', ''),  # 测试关注重点
                    'switch_description': parsed_desc.get('switch_description', ''),  # 开关说明
                    'experiment_report_url': parsed_desc.get('experiment_report_url', ''),  # 实验报告地址
                    'created_at': mr_info.get('created_at', ''),
                    'merged_at': mr_info.get('merged_at', '')
                }

                detailed_mrs.append(detailed_mr)
                tapd_status = "已关联" if tapd_url else "未关联"
                print(f"成功解析 MR !{mr_info['iid']}: {mr_info.get('title', '')} (TAPD: {tapd_status})")
            except Exception as e:
                print(f"处理 MR {mr_info['iid']} 时出错：{e}")
        
        # 获取可能重复的提交
        possible_duplicate_commits_list = []
        get_possible_duplicate_commits = self.compare_branches(from_ref='master', to_ref=branch_name)
        possible_duplicate_commits = get_possible_duplicate_commits.get('commits', [])
        print(f"可能重复的提交 个数：{len(possible_duplicate_commits)}")
        for commit in possible_duplicate_commits:
            print(f"可能重复 的 提交信息：{commit.get('title', '')}")
            possible_duplicate_commits_list.append(commit)
        
        return detailed_mrs, possible_duplicate_commits_list
        

    def get_branch_diff_mrs_with_details(self, from_branch: str, to_branch: str = 'master',
                                       days_back: int = 30) -> List[Dict[str, Any]]:
        """
        对比两个分支获取差异 MR，并解析每个 MR 的详细信息

        Args:
            from_branch: 源分支
            to_branch: 目标分支（默认：'master'）
            days_back: 回溯天数（默认：30）

        Returns:
            包含详细信息的 MR 列表，每个 MR 包含以下字段：
            [
                {
                    'iid': MR的IID,
                    'id': MR的ID,
                    'title': MR标题（需求）,
                    'creator': 需求创建人
                    'type': 需求类型
                    'author': MR作者（终端开发）,
                    'mr_url': MR链接,
                    'tapd_url': TAPD工作项链接（需求单）,
                    'tapd_workitems': TAPD工作项详细信息列表,
                    'is_pre_embedded': 是否预埋（是/否）,
                    'integration_test_focus': 测试关注重点,
                    'switch_description': 开关说明,
                    'experiment_report_url': 实验报告地址,
                    'created_at': 创建时间,
                    'merged_at': 合并时间
                }
            ]
        """
        # 1. 获取两个分支的差异 MR IID 列表
        mr_iids, no_mr_id_list = self.get_mr_iids_from_version_comparison(from_branch, to_branch)

        if not mr_iids and not no_mr_id_list:
            print(f"未找到 {from_branch} 和 {to_branch} 之间的差异 MR")
            return []

        # print(f"找到 {len(mr_iids)} 个差异 MR: {mr_iids}")
        print(f"找到 {len(mr_iids)} 个差异 MR")
        # 打印第一个 MR
        print(f"get_branch_diff_mrs_with_details == 第一个 MR:\n {mr_iids[0]}")

        # 2. 获取这些 MR 的基本信息（根据指定时间周期的mr列表转换mr_iid到mr_id），并过滤是否是插件mr
        recent_mrs = self.get_recent_merged_mrs_by_iids(mr_iids, days_back)
        print(f"获取到 {len(recent_mrs)} 个最近合并的 MR")

        # recent_mrs = self.get_recent_merged_mrs_by_iids()

        # 3. 批量获取 TAPD 工作项信息
        print("正在获取 TAPD 工作项信息...")
        tapd_workitems_batch = self.get_tapd_workitems_batch([str(mr['iid']) for mr in recent_mrs])

        # 4. 为每个 MR 解析详细信息
        detailed_mrs = []
        for mr in recent_mrs:
            try:
                mr_iid_str = str(mr['iid'])

                # 解析 MR 描述
                parsed_desc = self.parse_mr_description(mr.get('description', ''))

                # 构建 MR 链接
                mr_url = self.build_mr_url(mr['iid'])

                # 获取 TAPD 工作项信息
                tapd_workitems = tapd_workitems_batch.get(mr_iid_str, [])

                tapd_url = ''
                if tapd_workitems:
                    tapd_urls, tapd_info = self.build_tapd_urls_from_workitems(tapd_workitems)
                    if tapd_urls:
                        # 拼接所有 TAPD 链接作为 需求单
                        tapd_url = "\n".join(tapd_urls.values())

                # 组装详细信息
                detailed_mr = {
                    'iid': mr['iid'],
                    'id': mr['id'],
                    'title': mr.get('title', ''),  # 需求
                    'creator': tapd_info.get('creator', ''),  # 需求创建人
                    'type': tapd_info.get('type', ''),  # 需求类型
                    'author': mr.get('author', ''),  # 终端开发
                    'mr_url': mr_url,  # MR链接
                    'tapd_url': tapd_url,  # 需求单
                    'tapd_workitems': tapd_workitems,  # TAPD 工作项详细信息
                    'is_pre_embedded': parsed_desc.get('is_pre_embedded', ''),  # 是否预埋
                    'integration_test_focus': parsed_desc.get('integration_test_focus', ''),  # 测试关注重点
                    'switch_description': parsed_desc.get('switch_description', ''),  # 开关说明
                    'experiment_report_url': parsed_desc.get('experiment_report_url', ''),  # 实验报告地址
                    'created_at': mr.get('created_at', ''),
                    'merged_at': mr.get('merged_at', '')
                }

                detailed_mrs.append(detailed_mr)
                tapd_status = "已关联" if tapd_url else "未关联"
                print(f"成功解析 MR !{mr['iid']}: {mr.get('title', '')} (TAPD: {tapd_status})")

            except Exception as e:
                print(f"解析 MR !{mr['iid']} 失败: {e}")
                continue
        
        # 拼接没有id的mr
        for no_mr_id_item in no_mr_id_list:
            try:
                # 解析 MR 描述
                parsed_desc = self.parse_mr_description(no_mr_id_item.get('message', ''))

                # 组装详细信息
                detailed_mr = {
                    'iid': '',
                    'id': '',
                    'title': no_mr_id_item.get('title', ''),  # 需求
                    'creator': '',  # 需求创建人
                    'type': '',  # 需求类型
                    'author': no_mr_id_item.get('author_name', ''),  # 终端开发
                    'mr_url': '',  # MR链接
                    'tapd_url': '',  # 需求单
                    'tapd_workitems': '',  # TAPD 工作项详细信息
                    'is_pre_embedded': parsed_desc.get('is_pre_embedded', ''),  # 是否预埋
                    'integration_test_focus': parsed_desc.get('integration_test_focus', ''),  # 测试关注重点
                    'switch_description': parsed_desc.get('switch_description', ''),  # 开关说明
                    'experiment_report_url': parsed_desc.get('experiment_report_url', ''),  # 实验报告地址
                    'created_at': no_mr_id_item.get('created_at', ''),
                    'merged_at': ''
                }
                detailed_mrs.append(detailed_mr)
                print(f"成功解析 MR（标题没有mr_id） : {no_mr_id_item.get('title', '')} ")

            except Exception as e:
                print(f"解析 MR {no_mr_id_item['title']} 失败: {e}")
                continue

        return detailed_mrs

    def build_tapd_url(self, workspace_id: str, tapd_id: str, type: str) -> str:
        """
        根据 workspace_id 和 tapd_id 构建 TAPD 工作项链接

        Args:
            workspace_id: TAPD 工作空间 ID
            tapd_id: TAPD 工作项 ID

        Returns:
            完整的 TAPD 工作项链接 URL
        """
        if type == 'BUG':
            return f"https://tapd.woa.com/tapd_fe/{workspace_id}/bug/detail/{tapd_id}"
        else:
            return f"https://tapd.woa.com/tapd_fe/{workspace_id}/story/detail/{tapd_id}"
            

    def build_tapd_urls_from_workitems(self, workitems: List[Dict[str, Any]]) -> Dict[str, str]:
        """
        从 TAPD 工作项列表批量构建 TAPD 链接

        Args:
            workitems: get_tapd_workitems 返回的工作项列表

        Returns:
            tapd_id 到 URL 的映射字典 和 tapd_info
        """
        urls = {}
        for item in workitems:
            tapd_id = str(item.get('tapd_id', ''))
            workspace_id = str(item.get('workspace_id', ''))
            tapd_info = self.tapd_client.get_simplified_ids_info(list([tapd_id]))
            # tapd单子类型：story/bug
            for tapd_item in tapd_info:
                type = tapd_item.get('type', '')
                if tapd_id and workspace_id:
                    urls[tapd_id] = self.build_tapd_url(workspace_id, tapd_id, type)
        # 默认返回第一个tapd单的信息
        return urls, tapd_info[0]

    def get_tapd_workitems_batch(self, mr_iids: List[str], item_type: str = 'mr') -> Dict[str, List[Dict[str, Any]]]:
        """
        批量查询多个 MR 的 TAPD 工作项

        Args:
            mr_iids: 合并请求 IID 列表
            item_type: 项目类型（默认：'mr'）

        Returns:
            MR IID 到 TAPD 工作项列表的映射字典
        """
        result = {}
        for mr_iid in mr_iids:
            try:
                workitems = self.get_tapd_workitems(mr_iid, item_type)
                result[mr_iid] = workitems
            except GitClientError as e:
                print(f"获取 MR {mr_iid} 的 TAPD 工作项失败: {e}")
                result[mr_iid] = []
        return result

    @classmethod
    def create_default_client(cls, project_id: str = "126979",
                             private_token: str = "_zhCOZW_1nf3_bHORMGD",
                             project_path: str = "yyb-android/TencentMobileAssistant") -> 'GitClient':
        """
        使用默认配置创建 GitClient 实例

        Args:
            project_id: 项目 ID（默认 仓库 TencentMobileAssistant）
            private_token: 私钥 （默认lichenlin的私钥，个人私钥查看：https://git.woa.com/profile/account）
            project_path: 项目的全路径（默认：yyb-android/TencentMobileAssistant）

        Returns:
            配置好的 GitClient 实例
        """
        return cls(project_id=project_id, private_token=private_token, project_path=project_path)


# 向后兼容的便捷函数
def get_mr_iid_list_from_version(version: str,
                                project_id: str = "126979",
                                private_token: str = "_zhCOZW_1nf3_bHORMGD") -> List[str]:
    """
    向后兼容的遗留函数
    从版本对比中获取 MR IID 列表

    Args:
        version: 源版本/分支/标签
        project_id: Git 项目 ID
        private_token: 认证令牌

    Returns:
        合并请求 IID 列表
    """
    client = GitClient(project_id=project_id, private_token=private_token)
    return client.get_mr_iids_from_version_comparison(version)


if __name__ == "__main__":
    git_client = GitClient.create_default_client()

    # try:
    #     print("=== 分支差异 MR 分析 ===")
        # detailed_mrs = git_client.get_branch_diff_mrs_with_details("release-9.0.0", "master", days_back=30)

    #     if detailed_mrs:
    #         print(f"找到 {len(detailed_mrs)} 个差异 MR")
    #     else:
    #         print("未找到差异 MR")

    # except GitClientError as e:
    #     print(f"分支差异 MR 分析失败: {e}")


    # mr_iids = git_client.get_mr_iids_from_version_comparison("release-8.9.9", "master")
    # print(mr_iids)

    # recent_mrs = git_client.get_recent_merged_mrs_by_iids(mr_iids, days_back=30)
    # print(recent_mrs)

    # branch_info = git_client.get_mr_by_date("900")
    # mr_details = git_client.get_mr_list_details_by_date("901")
    # print(f"mr_details len: {len(mr_details)}")
    # print(branch_info)


    # comparison_result = git_client.compare_branches(from_ref='master', to_ref='release-9.0.0', straight=False)
    comparison_result = git_client.compare_branches(from_ref='release-9.0.0', to_ref='master', straight=False)
    commits = comparison_result.get('commits', [])
    print(f"提交列表个数：{len(commits)}")

    for commit in commits:
        print(f"提交信息：{commit.get('title', '')}")
        
    
        # commit_id = commit.get('id', '')
        # print(f"提交 ID：{commit_id}")
        # get_commit_branches = git_client.get_commit_branches(sha=commit_id)
        # print(f"提交分支：{get_commit_branches}")
        # for branch in get_commit_branches:
        #     print(f"分支名称：{branch}")
        #     if branch.startswith('cherry-pick'):
        #         print(f"cherry-pick 分支：{branch}")
        #         commit_list = git_client.get_all_commits(ref_name=branch)
        #         # 第一个就是最新的提交，获取第一个提交的id
        #         new_commit_id = commit_list[0].get('id', '')
        #         print(f"最新的提交 ID：{new_commit_id}")
        #         new_commit_branches = git_client.get_commit_branches(sha=new_commit_id)
        #         print(f"cherry-pick 分支 的 最新的提交 所属：{new_commit_branches}")


        
