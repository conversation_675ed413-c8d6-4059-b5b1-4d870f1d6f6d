import requests
import json
from datetime import datetime, timedelta

from common.logs.logger import app_logger


# 参考文档：https://iwiki.woa.com/p/4013884153
# 统计数据：https://iwiki.woa.com/p/4010805240

class BuglyClient:
    def __init__(self):
        self._api_url_decrypt = "http://api.apigw.oa.com/rmonitor/v1/diagnose/decrypt_log"
        self._api_url_query = "http://api.apigw.oa.com/rmonitor/v1/diagnose/query_log_upload_list"
        self._api_url_query_by_index = "http://api.apigw.oa.com/rmonitor/v1/diagnose/query_log_upload_list_by_index"
        self._api_url_sum = "http://api.apigw.oa.com/rmonitor/v1/trend"  # 查询指定版本的统计数据
        
        self._headers = {
            "X-Gateway-Stage": "RELEASE",
            "X-Gateway-SecretId": "yyb_download_bugly_log",  # 申请的应用名
            "X-Gateway-SecretKey": "9b7bfd9e-80dd-4ddc-bd29-66b01aea",  # 应用名的密钥
            "X-ProductId": "900026256", # 产品ID
            "X-ProductKey": "Ty9B7jq98k6RNhdy", # 产品ID的key
            "Content-Type": "application/json"
        }

    # 根据 时间、guid等 查询日志列表
    def query_log_upload_list(self, begin_time, end_time, guid, offset=0, size=1, cmd_type=3, label="", summary="", upload_id=None, app_version=""):
        body = {
            "product_id": "900026256",  # product_id
            "offset": offset,  # 必填
            "size": size, # 必填 
            "cmd_type": cmd_type,  # 必填  命令类型，1：日志捞取; 2：日志采集 3：日志自动上报;
            "begin_time": begin_time,  # 必填  结束时间 ‘2025-03-10 00:00:00’
            "end_time": end_time,  # 必填 结束时间 ‘2025-03-10 00:00:00’
            "guid": guid # 过滤字段 页面的“设备ID”
        }
        response = requests.post(self._api_url_query, headers=self._headers, data=json.dumps(body), timeout=60)
        result =  self._handle_response(response)
        # 解析回包
        return self.parse_log_list(result)

    # 根据 indexKey 查询日志列表
    def query_log_by_index_key(self, index_key):
        body = {
            "product_id": self._headers["X-ProductId"],
            "index_key": index_key
        }
        response = requests.post(self._api_url_query_by_index, headers=self._headers, data=json.dumps(body), timeout=60)
        result =  self._handle_response(response)
        app_logger.info(f"indexKey 查询日志 回包: {result}")
        # 解析回包
        return self.parse_log_list(result)
    
    # 解密日志
    def decrypt_log(self, log_url, sync=True):
        body = {
            "product_id": self._headers["X-ProductId"],
            "log_url": log_url,
            "sync": sync
        }
        response = requests.post(self._api_url_decrypt, headers=self._headers, data=json.dumps(body), timeout=60)
        result =  self._handle_response(response)
        # 解析回包
        return self.parse_decrypt_log(result)

    def _handle_response(self, response):
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"请求失败，状态码: {response.status_code}, 错误信息: {response.text}")

    # 解析回包  -- 日志列表
    def parse_log_list(self, result):
        log_data = []  # 用于存储解析后的日志信息

        if 'logInfos' in result and isinstance(result['logInfos'], list):
            for log_info in result['logInfos']:
                # guid
                guid = log_info.get('guid')
                # 命令类型 1：日志捞取; 2：日志采集 3：日志自动上报;
                cmd_type = log_info.get('cmdType')
                # 上传状态，0.未知 1.配置失败 2.配置成功 3.上传失败 4.上传成功
                upload_status = log_info.get('uploadStatus')
                # 未解密的URL链接
                log_url = log_info.get('logUrl')
                # 解密后的URL链接
                decrypted_url = log_info.get('decryptedUrl')
                # 解密状态
                decrypt_status = log_info.get('decryptStatus')

                # 将提取的信息存储到字典中
                log_entry = {
                    'guid': guid,
                    'cmd_type': cmd_type,
                    'upload_status': upload_status,
                    'log_url': log_url,
                    'decrypted_url': decrypted_url,
                    'decrypt_status': decrypt_status
                }
                log_data.append(log_entry)  # 将字典添加到列表中
            app_logger.info(f"解析出的日志列表: {log_data}")
        else:
            app_logger.warning("没有找到日志信息。")

        return log_data  # 返回解析后的日志信息列表
    
    # 解析回包 -- 日志解密
    def parse_decrypt_log(self, result):
        decrypted_url = result.get('decryptedUrl')
        decrypt_status = result.get('decryptStatus')
        decrypt_info = {
            'decrypted_url': decrypted_url,
            'decrypt_status': decrypt_status    
        }
        app_logger.info(f"解析出的日志解密信息: {decrypt_info}")
        return decrypt_info
        
    def query_crash_and_anr_by_version(self, start_time, end_time, product_versions, biz_type="crash",  time_window = "1d", platform="", operator="OR"):
        """
        查询指定版本的 crash，anr，error，foom 统计数据

        :param start_time: 开始时间，格式 ISO8601，如 "2025-05-14T00:00:00+08:00"
        :param end_time: 结束时间，格式 ISO8601
        :param product_versions: 版本号列表，如 ["8.8.8_8883130_5742"]
        :param biz_type: 业务类型，crash，anr，error，foom
        :param time_window: 时间窗口：1d，1h，5m，如果选择1h或者5m，则start_time和end_time间隔必须小于24h
        :param platform: 平台，默认空字符串
        :param operator: 操作符，默认为 OR
        :return: 解析后的数据字典，包含聚合后的统计数据，或抛出异常
        """
        if not isinstance(product_versions, list):
            raise ValueError("product_versions 必须是列表")

        body = {
            "platform": platform,
            "biz_type": biz_type,
            "start_time": start_time,
            "end_time": end_time,
            "time_window": time_window,
            "user_customs": [
                {
                    "key": "product_version",
                    "values": product_versions,
                    "operator": operator
                }
            ]
        }

        response = requests.post(self._api_url_sum, headers=self._headers, json=body, timeout=60)
        if response.status_code != 200:
            raise Exception(f"请求失败，状态码: {response.status_code}, 错误信息: {response.text}")

        result = response.json()
        base_rsp = result.get("base_rsp", {})
        code = base_rsp.get("code")
        msg = base_rsp.get("msg")

        if code != 0:
            raise Exception(f"API返回错误，code={code}, msg={msg}")

        resp = result.get("response", {})
        data_list = resp.get("data", [])

        # 如果data是空列表，返回空的每日数据
        if not data_list:
            return {
                "product_id": resp.get("product_id"),
                "platform": resp.get("platform"),
                "daily_data": []
            }

        # 处理每天的数据
        daily_data = []

        for data_item in data_list:
            # 解析时间并转换为日期格式
            time_str = data_item.get("time", "")
            if time_str:
                # 从 '2025-05-13T16:00:00Z' 提取日期部分
                date_part = time_str.split("T")[0]  # 获取 '2025-05-13'

                # API返回的是UTC时间，需要转换为北京时间的业务日期
                # '2025-05-13T16:00:00Z' (UTC) -> '2025-05-14' (北京时间业务日期)
                try:
                    date_obj = datetime.strptime(date_part, "%Y-%m-%d")
                    # UTC时间加8小时转换为北京时间，如果超过当天则日期+1
                    # 这里简化处理：直接将日期+1天来对应北京时间的业务日期
                    next_day = date_obj + timedelta(days=1)
                    formatted_date = next_day.strftime("%Y/%m/%d")  # 转换为 '2025/05/14' 格式
                except ValueError:
                    # 如果解析失败，使用原始逻辑
                    formatted_date = date_part.replace("-", "/")
            else:
                formatted_date = ""

            daily_data.append({
                "date": formatted_date,
                "original_time": time_str,
                "affect_count": data_item.get("affect_count", 0),
                "affect_user_count": data_item.get("affect_user_count", 0),
                "affect_device_count": data_item.get("affect_device_count", 0),
                "access_count": data_item.get("access_count", 0),
                "access_user_count": data_item.get("access_user_count", 0),
                "access_device_count": data_item.get("access_device_count", 0),
                "access_launch_times": data_item.get("access_launch_times", 0)
            })

        return {
            "product_id": resp.get("product_id"),  # 与Http Header的X-ProductId字段一致
            "platform": resp.get("platform"), # 与请求参数一致
            "daily_data": daily_data  # 每日的详细数据
        }
    
    def query_device_crash_rate(self, start_date, end_date, product_versions, platform="", operator="OR"):
        """
        计算每日设备crash率 = crash设备数 / 联网设备数

        :param start_date: 开始日期，格式：'2025-05-14' 或 ISO8601格式
        :param end_date: 结束日期，格式：'2025-05-14' 或 ISO8601格式
        :param product_versions: 版本号列表
        :param platform: 平台，默认空字符串
        :param operator: 操作符，默认 OR
        :return: list，包含每日的crash设备数、联网设备数和crash率
        """
        # 转换日期格式
        start_time = self._convert_date_to_iso(start_date)
        end_time = self._convert_date_to_iso(end_date)
        # 查询 crash 数据
        crash_data = self.query_crash_and_anr_by_version(
            start_time=start_time,
            end_time=end_time,
            product_versions=product_versions,
            biz_type="crash",
            platform=platform,
            operator=operator
        )

        daily_crash_rates = []
        for daily_item in crash_data.get("daily_data", []):
            crash_device_count = daily_item.get("affect_device_count", 0)
            network_device_count = daily_item.get("access_device_count", 0)

            # 计算 crash 率，避免除零
            crash_rate = (crash_device_count / network_device_count) if network_device_count > 0 else 0

            daily_crash_rates.append({
                "date": daily_item.get("date"),
                "crash_device_count": crash_device_count,
                "network_device_count": network_device_count,
                "crash_rate": crash_rate
            })

        return daily_crash_rates

    def query_device_anr_rate(self, start_date, end_date, product_versions, platform="", operator="OR"):
        """
        计算每日设备ANR率 = ANR设备数 / 联网设备数

        :param start_date: 开始日期，格式：'2025-05-14' 或 ISO8601格式
        :param end_date: 结束日期，格式：'2025-05-14' 或 ISO8601格式
        :param product_versions: 版本号列表
        :param platform: 平台，默认空字符串
        :param operator: 操作符，默认 OR
        :return: list，包含每日的ANR设备数、联网设备数和ANR率
        """
        # 转换日期格式
        start_time = self._convert_date_to_iso(start_date)
        end_time = self._convert_date_to_iso(end_date)
        # 查询 ANR 数据
        anr_data = self.query_crash_and_anr_by_version(
            start_time=start_time,
            end_time=end_time,
            product_versions=product_versions,
            biz_type="anr",
            platform=platform,
            operator=operator
        )

        daily_anr_rates = []
        for daily_item in anr_data.get("daily_data", []):
            anr_device_count = daily_item.get("affect_device_count", 0)
            network_device_count = daily_item.get("access_device_count", 0)

            # 计算 ANR 率，避免除零
            anr_rate = (anr_device_count / network_device_count) if network_device_count > 0 else 0

            daily_anr_rates.append({
                "date": daily_item.get("date"),
                "anr_device_count": anr_device_count,
                "network_device_count": network_device_count,
                "anr_rate": anr_rate
            })

        return daily_anr_rates

    def _convert_date_to_iso(self, date_str):
        """
        将简单日期格式转换为ISO8601格式

        :param date_str: 日期字符串，支持格式：'2025-05-14' 或 '2025-05-14T00:00:00+08:00'
        :return: ISO8601格式的时间字符串
        """
        # 如果已经是ISO格式，直接返回
        if 'T' in date_str:
            return date_str

        # 如果是简单日期格式，转换为ISO格式
        try:
            # 解析日期并直接转换为ISO格式，不做日期偏移
            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
            return date_obj.strftime("%Y-%m-%dT00:00:00+08:00")
        except ValueError:
            raise ValueError(f"日期格式错误，请使用 'YYYY-MM-DD' 格式: {date_str}")

    def get_crash_and_anr_rates_by_date(self, start_date, end_date, product_versions, platform="", operator="OR"):
        """
        根据日期和产品版本获取crash率和ANR率

        :param start_date: 开始日期，格式：'2025-05-14' 或 ISO8601格式
        :param end_date: 结束日期，格式：'2025-05-14' 或 ISO8601格式
        :param product_versions: 版本号列表
        :param platform: 平台，默认空字符串
        :param operator: 操作符，默认 OR
        :return: list，包含每日的crash率和ANR率数据，便于直接使用
        """
        # 获取crash率数据
        crash_rates = self.query_device_crash_rate(
            start_date=start_date,
            end_date=end_date,
            product_versions=product_versions,
            platform=platform,
            operator=operator
        )

        # 获取ANR率数据
        anr_rates = self.query_device_anr_rate(
            start_date=start_date,
            end_date=end_date,
            product_versions=product_versions,
            platform=platform,
            operator=operator
        )

        # 按日期组织数据
        date_data = {}

        # 处理crash率数据
        for crash_data in crash_rates:
            date = crash_data["date"]
            if date not in date_data:
                date_data[date] = {"date": date}
            date_data[date]["crash_rate"] = crash_data["crash_rate"]
            date_data[date]["crash_device_count"] = crash_data["crash_device_count"]
            date_data[date]["network_device_count"] = crash_data["network_device_count"]

        # 处理ANR率数据
        for anr_data in anr_rates:
            date = anr_data["date"]
            if date not in date_data:
                date_data[date] = {"date": date}
            date_data[date]["anr_rate"] = anr_data["anr_rate"]
            date_data[date]["anr_device_count"] = anr_data["anr_device_count"]
            # network_device_count应该是一样的，但为了保险起见还是更新
            date_data[date]["network_device_count"] = anr_data["network_device_count"]

        # 转换为列表格式，按日期排序
        result = []
        for date in sorted(date_data.keys()):
            data = date_data[date]
            # 确保所有字段都存在，如果不存在则设为0
            result.append({
                "date": data.get("date", ""),
                "crash_rate": data.get("crash_rate", 0),
                "anr_rate": data.get("anr_rate", 0),
                "crash_device_count": data.get("crash_device_count", 0),
                "anr_device_count": data.get("anr_device_count", 0),
                "network_device_count": data.get("network_device_count", 0)
            })

        return result

    def get_rates_by_date_and_version(self, date, product_versions, platform="", operator="OR"):
        """
        根据指定日期和产品版本获取当天的crash率和ANR率

        :param date: 日期，格式如 "2025-05-14" 或 "2025/05/14"
        :param product_versions: 版本号列表
        :param platform: 平台，默认空字符串
        :param operator: 操作符，默认 OR
        :return: dict，包含当天的crash率和ANR率
        """
        # 标准化日期格式
        if "/" in date:
            date = date.replace("/", "-")  # 转换为 "2025-05-14" 格式

        # 获取数据（使用同一天作为开始和结束日期）
        rates_data = self.get_crash_and_anr_rates_by_date(
            start_date=date,
            end_date=date,
            product_versions=product_versions,
            platform=platform,
            operator=operator
        )

        # 返回第一天的数据（应该只有一天）
        if rates_data:
            return rates_data[0]

        # 如果没找到数据，返回默认值
        target_date = date.replace("-", "/")  # 转换为 "2025/05/14" 格式
        return {
            "date": target_date,
            "crash_rate": 0,
            "anr_rate": 0,
            "crash_device_count": 0,
            "anr_device_count": 0,
            "network_device_count": 0,
            "message": "未找到该日期的数据"
        }


if __name__ == "__main__":
    bugly_client = BuglyClient()

    # print("========== 通过 检索Key 获取日志列表 ===========")
    # log_list_by_index = bugly_client.query_log_by_index_key("e18b4ae9e954044cbdd56cdfdf22078b_1744698711058")
    # for log in log_list_by_index:
    #     print(log)  

    # print("========== 通过 guid 获取日志列表 ===========")
    # log_list_by_guid = bugly_client.query_log_upload_list(
    #     begin_time="2025-04-12 00:00:00",
    #     end_time="2025-04-16 00:00:00",
    #     guid="2219691476776293120"
    # )

    # # print(f"log_list_by_guid = {log_list_by_guid}")

    # # 直接遍历 log_list_by_guid 列表
    # for log in log_list_by_guid:
    #     print(f'log = {log}')  # 打印每个日志条目的信息

    # # 获取特定字段（例如 guid）
    # guid_list = [log['guid'] for log in log_list_by_guid] 

    # # 打印所有的 guid
    # for guid in guid_list:
    #     print(guid)

    # # decrypt_status = log['decrypt_status']
    # # log_url = log['log_url']
    
    # print("========== 解密 ===========")
    # for log in log_list_by_guid:
    #     decrypt_status = log['decrypt_status']
    #     print(f'decrypt_status = {decrypt_status}')
    #     if decrypt_status is not None and decrypt_status != 3:
    #         log_url = log['log_url']
    #         decrypted_result = bugly_client.decrypt_log(log_url)
    #         print(f'decrypted_result = {decrypted_result}')
    #         print(f'decrypted_url = {decrypted_result.get("decrypted_url")}')
    #         print(f'decrypt_status = {decrypted_result.get("decrypt_status")}')

    # decrypted_result = bugly_client.decrypt_log(log_url)
    # print(f'decrypted_url = {decrypted_result.get("decrypted_url")}')
    # print(f'decrypt_status = {decrypted_result.get("decrypt_status")}')

    try:
        print("\n========== 基本统计数据查询 ==========")
        result = bugly_client.query_crash_and_anr_by_version(
            start_time="2025-05-14T00:00:00+08:00",
            end_time="2025-05-16T00:00:00+08:00",  # 扩展时间范围以获取多天数据
            product_versions=["8.8.8_8883130_5742"],
            biz_type="crash"
        )
        print("查询结果:")
        print(result)
        print("\n每日数据:")
        for daily_item in result.get("daily_data", []):
            print(f"日期: {daily_item['date']}, Crash设备数: {daily_item['affect_device_count']}, 联网设备数: {daily_item['access_device_count']}")
    except Exception as e:
        print(f"查询失败: {e}")

    print("\n========== 每日Crash率 ==========")
    crash_results = bugly_client.query_device_crash_rate(
        start_date="2025-05-14",  
        end_date="2025-05-16",    
        product_versions=["8.8.8_8883130_5742"]
    )

    for crash_result in crash_results:
        print(f"日期: {crash_result['date']}")
        print(f"Crash设备数: {crash_result['crash_device_count']}")
        print(f"联网设备数: {crash_result['network_device_count']}")
        print(f"设备Crash率: {crash_result['crash_rate']:.4%}")
        print("-" * 40)

    print("\n========== 每日ANR率 ==========")
    anr_results = bugly_client.query_device_anr_rate(
        start_date="2025-05-14", 
        end_date="2025-05-16",   
        product_versions=["8.8.8_8883130_5742"]
    )

    for anr_result in anr_results:
        print(f"日期: {anr_result['date']}")
        print(f"ANR设备数: {anr_result['anr_device_count']}")
        print(f"联网设备数: {anr_result['network_device_count']}")
        print(f"设备ANR率: {anr_result['anr_rate']:.4%}")
        print("-" * 40)

    print("\n========== 按日期获取crash和ANR率 ==========")
    combined_rates = bugly_client.get_crash_and_anr_rates_by_date(
        start_date="2025-05-14", 
        end_date="2025-05-16",
        product_versions=["8.8.8_8883130_5742"]
    )

    # 现在外部用户可以直接遍历列表，无需处理字典键值
    for daily_data in combined_rates:
        print(f"日期: {daily_data['date']}")
        print(f"Crash率: {daily_data['crash_rate']:.4%}")
        print(f"ANR率: {daily_data['anr_rate']:.4%}")
        print(f"Crash设备数: {daily_data['crash_device_count']}")
        print(f"ANR设备数: {daily_data['anr_device_count']}")
        print(f"联网设备数: {daily_data['network_device_count']}")
        print("-" * 40)

    # 如何直接获取特定索引的数据
    if combined_rates:
        first_day = combined_rates[0]
        print(f"\n第一天数据直接访问:")
        print(f"日期: {first_day['date']}, Crash率: {first_day['crash_rate']:.4%}, ANR率: {first_day['anr_rate']:.4%}")

    print("\n========== 指定日期查询 ==========")
    # 查询指定日期的数据
    specific_date_result = bugly_client.get_rates_by_date_and_version(
        date="2025-05-14",  
        product_versions=["8.8.8_8883130_5742"]
    )

    print(f"指定日期查询结果:")
    print(f"日期: {specific_date_result['date']}")
    print(f"Crash率: {specific_date_result['crash_rate']:.4%}")
    print(f"ANR率: {specific_date_result['anr_rate']:.4%}")
    print(f"Crash设备数: {specific_date_result['crash_device_count']}")
    print(f"ANR设备数: {specific_date_result['anr_device_count']}")
    print(f"联网设备数: {specific_date_result['network_device_count']}")
    if 'message' in specific_date_result:
        print(f"消息: {specific_date_result['message']}")

    print("\n========== 便捷调用 ==========")
    print("# 获取单日数据:")
    print("result = bugly_client.get_rates_by_date_and_version('2025-05-14', ['8.8.8_8883130_5742'])")
    print(f"# 结果: Crash率={specific_date_result['crash_rate']:.4%}, ANR率={specific_date_result['anr_rate']:.4%}")

    print("\n# 获取多日数据:")
    print("results = bugly_client.get_crash_and_anr_rates_by_date('2025-05-14', '2025-05-16', ['8.8.8_8883130_5742'])")
    print(f"# 结果: 共{len(combined_rates)}天数据，可直接遍历使用")
