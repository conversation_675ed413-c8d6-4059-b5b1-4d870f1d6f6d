import requests
import zipfile
import os
import re
import mimetypes
from datetime import datetime, timedelta

from common.logs.logger import app_logger
from common.client.bugly_client import BuglyClient
from common.error.detailed_value_error import raise_value_error, ErrorCode

class FetchLogClient:
    def __init__(self, download_link=None, log_save_path=None):
        self._download_link = download_link
        self._zip_file_name = log_save_path
        self._unzip_folder = log_save_path
        self._is_single_file = False
        self._single_file_path = None
        # 设置文件存储路径
        self._set_zip_file_name()

    # 根据 download_link 获取日志路径
    def fetch_log_from_url(self):
        # 下载
        self.download_log()
        
        # 如果是单个文件直接返回文件路径
        if self._is_single_file:
            return self._single_file_path
            
        # 如果是ZIP文件则继续解压、获取相应的日志文件路径
        return self.unzip_and_fetch_log()
    
    # 解压 并 返回对应的日志文件路径
    def unzip_and_fetch_log(self, zip_path =""):
        base_path = self.unzip_file(zip_path)
        logs_path = self.get_log_path(base_path)
        return logs_path
    
    # 设置文件存储路径
    def _set_zip_file_name(self):
        app_logger.info(f"self._zip_file_name:{self._zip_file_name}")
        app_logger.info(f"self._unzip_folder:{self._unzip_folder}")
        current_time = datetime.now().strftime("%Y-%m-%d %H-%M-%S-%f")
        # 获取当前文件的目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 设置压缩文件的路径
        # 如果没有传入文件路径 默认设置根目录下的data文件夹下。
        if self._zip_file_name:
            self._zip_file_name = os.path.join(self._zip_file_name, f'log_{current_time}.zip')
        else:
            # 压缩文件的路径
            self._zip_file_name = os.path.join(current_dir, '../data', f'log_{current_time}.zip')
        zip_file_target_dir = os.path.dirname(os.path.abspath(self._zip_file_name))
        if not os.path.exists(zip_file_target_dir):
            os.makedirs(zip_file_target_dir)

        
        # 设置解压文件的路径
        # 如果没有传入文件路径 默认设置根目录下的data文件夹下。
        if self._unzip_folder:
            self._unzip_folder = os.path.join(self._unzip_folder, f'log_{current_time}')
        else:
            # 解压文件的路径
            self._unzip_folder = os.path.join(current_dir, '../data', f'log_{current_time}')
        unzip_folder_target_dir = os.path.dirname(os.path.abspath(self._unzip_folder))
        if not os.path.exists(unzip_folder_target_dir):
            os.makedirs(unzip_folder_target_dir)
    
    # 通过 index_key 获取真正的下载链接
    def parse_download_link_by_index_key(self):
        index_key = ''
        # 使用正则表达式提取 index 参数
        key_pattern = r'[?&]key=([^&]+)'
        match = re.search(key_pattern, self._download_link)
        if match:
            index_key = match.group(1)
            app_logger.info(f"下载链接的 index_key: {index_key}")
            # 获取真正的下载链接
            bugly_client = BuglyClient()
            log_list_by_index_key = bugly_client.query_log_by_index_key(index_key)  
            for log in log_list_by_index_key:
                decrypt_status = log['decrypt_status']
                app_logger.info(f'decrypt_status = {decrypt_status}')
                if decrypt_status is not None and decrypt_status != 3:
                    log_url = log['log_url']
                    decrypted_result = bugly_client.decrypt_log(log_url)
                    decrypted_url = decrypted_result.get("decrypted_url")
                    if decrypted_url is not None and decrypted_url != '':
                        self._download_link = decrypted_result.get("decrypted_url")
                        break
                    else:
                        raise_value_error(ErrorCode.LOG_DOWNLOAD_LINK_INVALID_NO_DECRYPTED_URL, message="日志下载链接无效，没有获取到解密后的链接")
                        # raise ValueError(f"日志下载链接无效，没有获取到解密后的链接")
                else:
                    decrypted_url = log['decrypted_url']
                    app_logger.info(f"解密后的链接 decrypted_url: {decrypted_url}")
                    if decrypted_url is not None and decrypted_url != '':
                        self._download_link = decrypted_url
                        break
                    else:
                        raise_value_error(ErrorCode.LOG_DOWNLOAD_LINK_INVALID_NO_DECRYPTED_URL, message="日志下载链接无效，没有获取到解密后的链接")
                        # raise ValueError("日志下载链接无效，没有获取到解密后的链接")
        else:
            app_logger.info(f"日志下载链接 未找到有效的 index key")
            raise_value_error(ErrorCode.LOG_DOWNLOAD_LINK_MISSING_INDEX_KEY, message="日志下载链接无效，没有获取到解密后的链接")
            # raise ValueError("日志下载链接 未找到有效的 index key")
    
    # 下载日志
    def download_log(self):
        if not self._download_link:
            app_logger.error("下载链接为空")
            # raise ValueError("日志下载链接为空，请提供有效的下载链接")
            raise_value_error(ErrorCode.LOG_DOWNLOAD_LINK_EMPTY, message="日志下载链接为空，请检查输入格式，提供有效的下载链接")
        
        # 判断下载链接是否带 index key
        print(f"下载链接: {self._download_link}")
        if '&key=' in self._download_link:
            # 通过 index_key 获取真正的下载链接
            self.parse_download_link_by_index_key()
            
        # 发送 HEAD 请求获取文件类型
        try:
            head_response = requests.head(self._download_link)
            content_type = head_response.headers.get('Content-Type', '')
            content_disposition = head_response.headers.get('Content-Disposition', '')
            app_logger.info(f"self._download_link = {self._download_link}")
            # 检查是否是单个日志文件而非 ZIP 文件
            if 'application/zip' not in content_type and "cos.ap-nanjing.myqcloud.com" not in self._download_link:
                app_logger.info(f"检测到单个日志文件，Content-Type: {content_type}")
                return self._download_single_file()
        except Exception as e:
            app_logger.warning(f"HEAD 请求失败，将尝试直接下载: {str(e)}")
        
        # 下载 ZIP 文件
        response = requests.get(self._download_link)

        # 检查请求是否成功
        if response.status_code == 200:
            # 检查响应内容类型，确定是否为 ZIP 文件
            content_type = response.headers.get('Content-Type', '')
            
            # 如果不是 ZIP 文件，作为单个日志文件处理
            if 'application/zip' not in content_type and not self._is_zip_content(response.content):
                app_logger.info(f"下载的内容不是 ZIP 文件，将作为单个日志文件处理, Content-Type: {content_type}")
                return self._save_as_single_file(response.content)
            
            # 打开文件并写入内容
            with open(self._zip_file_name, "wb") as file:
                file.write(response.content)
            app_logger.info(f"ZIP 文件下载成功，下载到目录:{self._zip_file_name}")
            return True
        else:
            error_msg = f"下载失败，状态码：{response.status_code}"
            app_logger.error(error_msg)
            raise RuntimeError(error_msg)
            
    # 下载单个日志文件
    def _download_single_file(self):
        app_logger.info(f"开始下载单个日志文件: {self._download_link}")
        response = requests.get(self._download_link)
        
        if response.status_code == 200:
            return self._save_as_single_file(response.content)
        else:
            error_msg = f"单个文件下载失败，状态码：{response.status_code}"
            app_logger.error(error_msg)
            raise RuntimeError(error_msg)
    
    # 保存为单个日志文件
    def _save_as_single_file(self, content):
        # 从 URL 或内容类型推断文件扩展名
        filename = os.path.basename(self._download_link)
        if not filename or '.' not in filename:
            # 尝试从 Content-Type 猜测扩展名
            content_type = requests.head(self._download_link).headers.get('Content-Type', '')
            ext = mimetypes.guess_extension(content_type) or '.log'
            current_time = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"log_{current_time}{ext}"
        
        # 创建保存目录
        save_dir = os.path.dirname(self._unzip_folder)
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
            
        # 设置单个文件的保存路径
        self._single_file_path = os.path.join(save_dir, filename)
        
        # 保存文件
        with open(self._single_file_path, 'wb') as file:
            file.write(content)
            
        app_logger.info(f"单个日志文件下载成功，保存到: {self._single_file_path}")
        self._is_single_file = True
        return True
    
    def _is_zip_content(self, content: bytes) -> bool:
        """
        检查内容是否为ZIP文件（根据文件头魔数）
        支持三种常见ZIP文件头：
        - PK\x03\x04 : 普通ZIP文件
        - PK\x05\x06 : 空的ZIP文件（End of central directory record）
        - PK\x07\x08 : ZIP64格式的扩展记录
        """
        try:
            if not isinstance(content, (bytes, bytearray)):
                return False
            # 读取前4个字节
            header = content[:4]
            return header in (b"PK\x03\x04", b"PK\x05\x06", b"PK\x07\x08")
        except Exception:
            return False

    # 解压ZIP文件
    def unzip_file(self, zip_path =""):
        # 创建解压目录（如果不存在）
        if not os.path.exists(self._unzip_folder):
            os.makedirs(self._unzip_folder)

        # 解压ZIP文件
        if zip_path != "":
            self._zip_file_name = zip_path
        with zipfile.ZipFile(self._zip_file_name, 'r') as zip_ref:
            zip_ref.extractall(self._unzip_folder)
        app_logger.info(f"文件解压成功，解压到目录：{self._unzip_folder}")
        return self._unzip_folder
    
    # 根据 Base路径 获取日志文件夹路径
    def get_log_path(self, base_path):
        # 定义目标路径
        contents = os.listdir(base_path)
        # 找到日志文件夹
        subfolder_path = None
        for item in contents:
            item_path = os.path.join(base_path, item)
            
            # 如果item_path是文件的话, 就直接用base_path下的所有文件
            if not os.path.isdir(item_path):
                subfolder_path = base_path
                break
            # 过滤mac系统的文件夹
            if os.path.isdir(item_path) and not item.startswith('__MACOSX'):
                subfolder_path = item_path
                break
                
        if not subfolder_path:
            # raise ValueError(f"在解压目录 {base_path} 中未找到有效的日志文件夹")
            raise_value_error(ErrorCode.NO_VALID_LOG_FOLDER_IN_EXTRACTED_DIR, message=f"在解压目录 {base_path} 中未找到有效的日志文件夹")
            
        # return self.get_log_files(subfolder_path)
        return subfolder_path

  
if __name__ == "__main__":
    # 场景1：下载单个日志文件
    print("\n===== 场景1：下载单个日志文件 =====")
    download_link = 'https://cms.myapp.com/xy/yybtech/U5sFGrWn.log'
    log_save_path = 'log'
    downloader = FetchLogClient(download_link=download_link, log_save_path=log_save_path)
    logs_path = downloader.fetch_log_from_url()
    print(f'文件夹路径: {logs_path}')


    
    # 场景2：下载ZIP包
    print("\n===== 场景2：下载ZIP包 =====")
    download_link="https://cms.myapp.com/xy/yybtech/WsnFAVsj.zip"
    downloader = FetchLogClient(download_link=download_link)
    logs_path = downloader.fetch_log_from_url()
    print(f'文件夹路径: {logs_path}')

    # 场景3：根据indexkey 获取下载链接
    print("\n===== 场景3：根据indexkey 获取下载链接 =====")
    download_link = "https://bugly.woa.com/v2/diagnose/command_delivery/list?pid=1&productId=900026256&tab=report&key=4b5cae33d24318171eada84e52aff73e_1746685066047"

    downloader = FetchLogClient(download_link=download_link)
    logs_path = downloader.fetch_log_from_url()
    print(f'文件夹路径: {logs_path}')
