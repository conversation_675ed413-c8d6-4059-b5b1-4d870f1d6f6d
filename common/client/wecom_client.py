import requests
import json

from common.logs.logger import app_logger
from common.error.detailed_value_error import raise_value_error, ErrorCode

# 参考链接：https://developer.work.weixin.qq.com/document/path/91880

class WecomClient:
    def __init__(self, webhook_key):
        self.url = f'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={webhook_key}'
    
    def send_message(self, chatid, content):
        data = {
            "chatid": chatid,
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
        
        response = requests.post(self.url, headers={'Content-Type': 'application/json'}, data=json.dumps(data))
        
        if response.status_code == 200:
            return response.json()  # 返回响应的 JSON 数据
        else:
            raise_value_error(
                ErrorCode.WECOM_WEBHOOK_ERROR,
                message=f"企微机器人webhook错误: {response.status_code}, {response.text}"
            )

if __name__ == "__main__":
    webhook_key = 'a87f69e7-c781-4ce0-a556-308a985ce5c9'  #  webhook key
    bot = WecomClient(webhook_key)
    
    try:
        response = bot.send_message("wrkSFfCgAAs0MpqtSjNGbDiVheEU5cKw", "日志分析结果加载中...")
        app_logger.info(f"消息发送成功, response:{response}")
    except Exception as e:
        app_logger.error(f"消息发送失败, error:{e}")
