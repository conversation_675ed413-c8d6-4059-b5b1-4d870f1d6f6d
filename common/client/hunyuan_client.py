import json
import uuid
import random

import requests
import sseclient
from common.logs.logger import app_logger
from common.error.detailed_value_error import raise_value_error, ErrorCode


# 请求混元大模型Client
class HunyuanClient:
    def __init__(self, ss_url, wsid, model, token, is_stream, enable_enhancement=False):
        """
        :param ss_url: Hunyuan Open API URL, 参考文档： https://iwiki.woa.com/p/4008510154
        :param model: 模型名称
        :param wsid: Hunyuan 模型 的 Workspace ID
        :param token: Token
        """
        self._ss_url = ss_url
        self._wsid = wsid
        self._model = model
        self._token = token
        self._is_stream = is_stream
        self._enable_enhancement = enable_enhancement
        self._headers = {
            "Content-Type": "application/json",
            "Authorization": f"{self._token}",
            "Wsid": self._wsid,
        }

    def call(self, prompt):
        return self.request(prompt)

    def request_deepseek(self, prompt, system_content="", temperature=0.7, top_p=0.6, top_k=20, repetition_penalty=1.05, output_seq_len=4096, max_input_seq_len=20480):
        """
        请求DeepSeek API
        :param prompt: 用户输入的提示
        :param system_content: 系统提示内容
        :param temperature: temperature
        :param top_p: top_p参数
        :param top_k: top_k参数
        :param repetition_penalty: 重复惩罚参数
        :param output_seq_len: 输出序列长度
        :param max_input_seq_len: 最大输入序列长度
        :return: 生成器，返回响应数据
        """

        enable_stream = self._is_stream
        save_log_without_thinking = ''
        save_log_with_thinking = ''
        # 是否已经结束思考阶段
        hasFinishedThinking = False
        query_id = "test_query_id_" + str(uuid.uuid4())
        app_logger.info(f'DeepSeek query_id = {query_id}')

        # DeepSeek API 请求格式
        json_data = {
            "model": self._model,
            "query_id": query_id,
            "messages": [
                {"role": "system", "content": system_content},
                {"role": "user", "content": prompt}
            ],
            "temperature": temperature,
            "top_p": top_p,
            "top_k": top_k,
            "repetition_penalty": repetition_penalty,
            "output_seq_len": output_seq_len,
            "max_input_seq_len": max_input_seq_len,
            "stream": enable_stream,
            "random_seed": random.randint(0, 2 ** 32 - 1),
            "beam_size": 1,
            "decoupled": 1,
            "is_stop": 0,
            "domain_index": -1,
            "enable_enhancement": False,
            "enable_sse": True,
            "is_moe": False,
        }

        resp = requests.post(self._ss_url, headers=self._headers, json=json_data, stream=enable_stream, verify=False)

        if resp.status_code != 200:
            result = f"Error: {resp.status_code}"
            app_logger.info(result)
            # 检查是否是503错误（超时）
            if resp.status_code == 503:
                try:
                    error_data = resp.json()
                    if 'error' in error_data and 'message' in error_data['error']:
                        result = f"Error 503: {error_data['error']['message']}"
                except:
                    result = "Error 503: Service temporarily unavailable"
            yield result
        else:
            app_logger.info(f'DeepSeek Output: {resp}')
            # 流式输出
            if enable_stream:
                client = sseclient.SSEClient(resp)
                for event in client.events():
                    if event.data != '':
                        try:
                            data_js = json.loads(event.data)

                            # 检查响应结构
                            if 'choices' in data_js and len(data_js['choices']) > 0:
                                choice = data_js['choices'][0]

                                # 处理delta格式的流式响应
                                if 'delta' in choice:
                                    delta = choice['delta']

                                    # 检查是否有回答内容
                                    if 'content' in delta and delta['content']:
                                        content = delta['content']
                                        print(content, end='', flush=True)

                                        # 根据思考阶段状态决定内容类型
                                        if hasFinishedThinking:
                                            yield {"data": content, "type": "answer"}
                                            save_log_without_thinking += content
                                        else:
                                            yield {"data": content, "type": "thinking"}
                                            save_log_with_thinking += content

                                    # 检查是否是结束标志
                                    if choice.get('finish_reason') == 'stop':
                                        print(f'\n[INFO] DeepSeek 响应结束')
                                        break

                                # 处理message格式的响应（非流式或最终响应）
                                elif 'message' in choice:
                                    message = choice['message']

                                    if 'content' in message and message['content']:
                                        content = message['content']
                                        print(content, end='', flush=True)
                                        yield {"data": content, "type": "answer"}
                                        save_log_without_thinking += content

                            # 检查是否有错误信息
                            elif 'error' in data_js:
                                error_info = data_js['error']
                                app_logger.info(f'DeepSeek API error: {error_info}')
                                raise ValueError(f"DeepSeek API error: {error_info}")
                            # 检查是否是事件信息（如thinking事件）
                            elif 'event' in data_js:
                                event_info = data_js['event']
                                if event_info.get('name') == 'thinking':
                                    # 检查思考状态变化
                                    thinking_state = event_info.get('state', 0)
                                    if thinking_state == 2:  # state=2 表示思考结束
                                        hasFinishedThinking = True
                                    app_logger.info(f'DeepSeek thinking event: {event_info}')
                                    continue

                        except json.JSONDecodeError as e:
                            app_logger.info(f'DeepSeek JSON解析异常: {str(e)}')
                            app_logger.info(f'DeepSeek raw event.data: {event.data}')
                        except Exception as e:
                            app_logger.info(f'DeepSeek 处理异常: {str(e)}')
                            app_logger.info(f'DeepSeek event.data: {event.data}')
                            try:
                                app_logger.info(f'DeepSeek parsed data_js: {data_js}')
                            except:
                                app_logger.info('DeepSeek data_js not available')

                app_logger.info(f'DeepSeek 思考内容: {save_log_with_thinking}')
                app_logger.info(f"DeepSeek 回答内容: {save_log_without_thinking}")

                if not save_log_without_thinking:
                    app_logger.info(f"DeepSeek 模型返回数据为空，可能是模型过载。 query_id = {query_id}")

                print(f'DeepSeek 思考内容: {save_log_with_thinking}')
                print(f'DeepSeek 回答内容: {save_log_without_thinking}')

                yield {"data": save_log_without_thinking, "type": "all_answer"}

            # 非流式输出
            else:
                try:
                    result = resp.json()
                    app_logger.info(f'DeepSeek non-stream result: {result}')

                    if 'error' in result:
                        app_logger.info(result)
                        error_msg = result['error'].get('message', 'Unknown error')
                        raise ValueError(error_msg)
                    else:
                        if 'choices' in result and len(result['choices']) > 0:
                            choice = result['choices'][0]
                            message = choice.get('message', {})
                            # 获取实际回答
                            content = message.get('content', '')
                            if content:
                                yield {"data": content, "type": "all_answer"}
                            else:
                                raise ValueError("返回内容为空，可能是混元模型过载")
                        else:
                            raise ValueError("No choices in response")

                except Exception as e:
                    app_logger.info(f'DeepSeek 解析响应异常: {str(e)}')
                    yield {"data": f"Parse error: {str(e)}", "type": "error"}

    def request(self, prompt):
        enable_stream = self._is_stream
        save_log_without_thinking = ''
        save_log_with_thinking = ''
        # 是否已经收到过思考结束的事件
        hasFinishedThinking = False  
        query_id = "query_id_" + str(uuid.uuid4())
        app_logger.info(f'query_id = {query_id}')
        json_data = {
            "model": self._model,
            "query_id": query_id,
            "messages": [
                {"role": "system", "content": ""},
                {"role": "user", "content": prompt}
            ],
            "temperature": 1,
            "top_p": 1,
            "top_k": 40,
            "repetition_penalty": 1,
            "output_seq_len": 8192,
            "max_input_seq_len": 32000,
            "stream": enable_stream,
            "enable_enhancement": self._enable_enhancement
        }

        resp = requests.post(self._ss_url, headers=self._headers, json=json_data,
                             stream=enable_stream)
        if resp.status_code != 200:
            result = "Error: {}".format(resp.status_code)
            app_logger.info(result)
            # return result
            yield result
        else:
            # 流式输出，参考文档：https://iwiki.woa.com/p/4008515885#Python%E8%B0%83%E7%94%A8%E7%A4%BA%E4%BE%8B
            if enable_stream:
                client = sseclient.SSEClient(resp)
                for event in client.events():
                    if event.data != '':
                        data_js = json.loads(event.data)
                        try:
                            # 检查是否有事件信息
                            if 'event' in data_js:
                                if data_js['event'].get('name', '') == 'thinking' and data_js['event'].get('state', -1) == 2:
                                    app_logger.info('结束思考')
                                    hasFinishedThinking = True 
                                    continue  
                            
                            if 'choices' in data_js and len(data_js['choices']) > 0 and 'delta' in data_js['choices'][0] and 'content' in data_js['choices'][0]['delta']:
                                stream_result = data_js['choices'][0]['delta']['content']
                                print(stream_result, end='', flush=True)
                                if hasFinishedThinking:
                                    yield {"data": stream_result, "type": "answer"}
                                    save_log_without_thinking += stream_result
                                else:
                                    yield {"data": stream_result, "type": "thinking"}
                                    save_log_with_thinking += stream_result
                              
                        except Exception as e:
                            app_logger.info(f'处理异常: {str(e)}')
                            app_logger.info(f'data_js = {data_js}')
                app_logger.info(f'思考内容: {save_log_with_thinking}')
                app_logger.info(f"回答内容: {save_log_without_thinking}")
                if not save_log_without_thinking:
                    app_logger.info(f"混元模型返回数据为空，可能是混元模型过载，具体咨询2000。 query_id = {query_id}")
                    # raise_value_error(ErrorCode.MODEL_OVERLOAD, message=f"混元模型过载，请重试。 query_id = {query_id}")
                    # raise ValueError(f"混元模型返回数据为空，可能是混元模型过载，具体咨询2000。 query_id = {query_id}")
                yield {"data": save_log_without_thinking, "type": "all_answer"}
                # return save_log_without_thinking
            # 非流式输出
            else:
                result = json.loads(resp.text)
                if 'error' in result:
                    app_logger.info(result)
                    raise Exception(result['error']['message'])
                else:
                    app_logger.info(result)
                    yield {"data": result['choices'][0]['message']['content'], "type": "all_answer"}
                    # return result['choices'][0]['message']['content']



if __name__ == '__main__':
    # DeepSeek API 配置（基于您提供的可用示例）
    ss_url = 'http://stream-server-online-openapi.turbotke.production.polaris:8080/openapi/chat/completions'
    model = "DeepSeek-R1-Online"
    wsid = "11417"
    token = "00ac8819-7488-4487-bfbd-17f4d760aed8"
    is_stream = True
    enable_enhancement = False

    hunyuan_client = HunyuanClient(ss_url, wsid, model, token, is_stream, enable_enhancement)

    # 测试DeepSeek API
    print("===== 测试 DeepSeek API =====")
    system_prompt = ""
    user_prompt = "你好"

    result = hunyuan_client.request_deepseek(
        prompt=user_prompt,
        system_content=system_prompt,
        temperature=0.7,
        top_p=0.6,
        top_k=20,
        repetition_penalty=1.05,
        output_seq_len=4096,
        max_input_seq_len=20480
    )

    if result:
        for data in result:
            print(f'DeepSeek Response: {data}')

    # print("\n===== 测试 Hunyuan API =====")
    # # 切换回Hunyuan配置进行对比测试
    # hunyuan_client._model = "Hunyuan-T1-32K"
    # hunyuan_client._wsid = "10697"
    # hunyuan_client._enable_enhancement = True
    # hunyuan_client._headers = {
    #     "Content-Type": "application/json",
    #     "Authorization": f"{token}",
    #     "Wsid": "10697",
    # }

    # result = hunyuan_client.call("海为什么是咸的")

    # if result:
    #     for data in result:
    #         print(f'Hunyuan Response: {data}')
