import requests
import json
import time
import random
import string

class BeaconAnalyticsClient:
    def __init__(self, main_app_key, platform_id=3, sdk_id="js", sdk_version="4.3.4-web", app_version=""):
        """
        初始化客户端
        :param main_app_key: 业务标识，从datahub申请的appkey值，必填
        :param platform_id: 平台ID，默认3，必填
        :param sdk_id: sdk标识，默认"js"，必填
        :param sdk_version: sdk版本，默认"4.3.4-web"
        :param app_version: 应用版本，默认空字符串
        """
        self.url = "https://otheve.beacon.qq.com/analytics/v2_upload"
        self.headers = {
            "Content-Type": "application/json;charset=UTF-8"
        }
        self.main_app_key = main_app_key
        self.platform_id = platform_id
        self.sdk_id = sdk_id
        self.sdk_version = sdk_version
        self.app_version = app_version

    @staticmethod
    def replace_symbol(value):
        """
        替换特殊字符：
        | -> %7C
        & -> %26
        = -> %3D
        + -> %2B
        """
        if not isinstance(value, str):
            return value
        try:
            return (value.replace("|", "%7C")
                         .replace("&", "%26")
                         .replace("=", "%3D")
                         .replace("+", "%2B"))
        except Exception as e:
            print("replace_symbol error:", e)
            return ""

    def convert_all_str(self, obj):
        """
        递归将所有字段转成字符串，并替换特殊字符
        """
        if isinstance(obj, dict):
            return {str(k): self.convert_all_str(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.convert_all_str(i) for i in obj]
        else:
            return self.replace_symbol(str(obj))

    @staticmethod
    def generate_random_a2(length=16):
        """生成16位随机字符串作为设备标识A2"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(length))

    @staticmethod
    def current_millis():
        """获取当前时间的毫秒时间戳字符串"""
        return str(int(time.time() * 1000))

    def build_default_payload(self, event_code="eventCode", event_map_value=None, app_version=None, sdk_version=None):
        """
        生成符合规范的默认请求体参数
        :param event_code: 事件名，默认"eventCode"
        :param event_map_value: 事件参数字典，默认{"app": "initialize"}
        :param app_version: 应用版本，覆盖初始化时的app_version
        :param sdk_version: sdk版本，覆盖初始化时的sdk_version
        :return: dict，完整请求体参数
        """
        if event_map_value is None:
            event_map_value = {"app": "initialize"}

        payload = {
            "appVersion": app_version if app_version is not None else self.app_version,
            "sdkId": self.sdk_id,
            "sdkVersion": sdk_version if sdk_version is not None else self.sdk_version,
            "mainAppKey": self.main_app_key,
            "platformId": self.platform_id,
            "common": {
                "A2": self.generate_random_a2()
            },
            "events": [
                {
                    "eventCode": event_code,
                    "eventTime": self.current_millis(),
                    "mapValue": event_map_value
                }
            ]
        }
        return payload

    def send_event(self, common_params=None, events=None, app_version=None, sdk_version=None):
        """
        发送事件数据
        :param common_params: dict，公共参数集合，必须包含设备标识等。如果为None，则自动生成A2
        :param events: list，事件集合，每个事件是dict，包含eventCode、eventTime、mapValue等。如果为None，则发送默认事件
        :param app_version: 应用版本，覆盖初始化时的app_version
        :param sdk_version: sdk版本，覆盖初始化时的sdk_version
        :return: requests.Response对象
        """
        if common_params is None:
            common_params = {"A2": self.generate_random_a2()}
        if events is None:
            events = [
                {
                    "eventCode": "eventCode",
                    "eventTime": self.current_millis(),
                    "mapValue": {"app": "initialize"}
                }
            ]

        payload = {
            "appVersion": app_version if app_version is not None else self.app_version,
            "sdkId": self.sdk_id,
            "sdkVersion": sdk_version if sdk_version is not None else self.sdk_version,
            "mainAppKey": self.main_app_key,
            "platformId": self.platform_id,
            "common": common_params,
            "events": events
        }

        # 转换所有字段为字符串并替换特殊字符
        payload_str = self.convert_all_str(payload)

        response = requests.post(self.url, headers=self.headers, data=json.dumps(payload_str))
        return response


if __name__ == "__main__":
    client = BeaconAnalyticsClient(main_app_key="0QQQ0KODCF4I6KGG", app_version="2.11.1")

    # 生成默认请求体参数
    payload = client.build_default_payload()

    import json
    print("生成的请求体参数:")
    print(json.dumps(payload, indent=4))

    # 发送请求
    response = client.send_event()
    print("状态码:", response.status_code)
    print("响应内容:", response.text)
