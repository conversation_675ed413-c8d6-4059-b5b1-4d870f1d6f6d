import requests
import json

# 通过检索Key获取日志列表 
api_url = "http://api.apigw.oa.com/rmonitor/v1/diagnose/query_log_upload_list_by_index" 

# 设置请求头
headers = {
    "X-Gateway-Stage": "RELEASE",
    "X-Gateway-SecretId": "yyb_download_bugly_log",  # 申请的应用名
    "X-Gateway-SecretKey": "9b7bfd9e-80dd-4ddc-bd29-66b01aea",  # 应用名的密钥
    "X-ProductId": "900026256", # 产品ID
    "X-ProductKey": "Ty9B7jq98k6RNhdy", # 产品ID的key
    "Content-Type": "application/json"
}

# 设置请求体
payload = {
    "product_id": "900026256",  # product_id
    "index_key": "c284a0379ea8121e5210b598978d9b59_1743829007961"  # 只能填 index key，不能填guid（会导致检索出 这个guid所有上报，多条数据会超时）
}

# 发送POST请求， 设置超时时间(秒)
response = requests.post(api_url, headers=headers, data=json.dumps(payload), timeout=60)  

# 检查响应状态
if response.status_code == 200:
    # 解析JSON响应
    response_data = response.json()
    print("日志列表:", json.dumps(response_data, indent=4, ensure_ascii=False))
else:
    print(f"请求失败，状态码: {response.status_code}, 错误信息: {response.text}")
