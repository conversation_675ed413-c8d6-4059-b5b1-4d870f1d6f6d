#!/usr/bin/env python3
"""
测试灰度数据收集修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__)))

from version_tool.gray_data.gray_data_collector import auto_collect_gray_data

def test_gray_data_collection():
    """测试灰度数据收集"""
    print("开始测试灰度数据收集修复...")
    
    try:
        # 使用version="900"进行测试
        result = auto_collect_gray_data("900")
        print(f"测试完成，结果: {result}")
        return True
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gray_data_collection()
    if success:
        print("测试通过")
    else:
        print("测试失败")
        sys.exit(1)
