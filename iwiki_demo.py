#!/usr/bin/env python3
"""
IWiki客户端使用演示
展示如何使用新增的创建文档和追加文档功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from common.client.iwiki_client import IWikiClient

def demo_create_document():
    """演示创建文档功能"""
    print("=== 创建文档演示 ===")
    
    client = IWikiClient()
    
    # 示例文档内容
    sample_body = """
## 启动速度分析报告

### 测试环境
- 测试版本: TMAF_899_P_7921
- 测试时间: 2025-01-01
- 测试设备: Android

### 测试结果

| 灰度新用户 | QUA | 常规热启动<br>(gap<50ms) | 常规冷启动<br>(gap<100ms) | 常规外call热启动<br>(gap<50ms) | 常规外call冷启动<br>(gap<50ms) |
|--------|--------------|---------|------|------|-------|
|实验组1|TMAF_899_P_7921|85.2%|78.9%|82.1%|75.6%|
|实验组2|TMAF_899_P_7921|86.1%|79.3%|83.2%|76.8%|
|对照组|TMAF_899_P_7921|84.7%|77.5%|81.9%|74.2%|

### 结论
实验组在各项指标上均有提升。
"""

    print("准备创建文档...")
    print("文档标题: 启动速度分析报告")
    print("空间: ailogs")
    print("父文档ID: 4015157988")
    
    # 注意：实际使用时取消注释以下代码
    # try:
    #     result = client.create_document(
    #         body=sample_body,
    #         title="启动速度分析报告",
    #         spacekey="ailogs",
    #         parentid=4015157988
    #     )
    #     
    #     print("\n创建结果:")
    #     if result['success']:
    #         print(f"✓ 文档创建成功")
    #         print(f"  文档ID (docid): {result['docid']}")
    #         print(f"  请求ID: {result['request_id']}")
    #         print(f"  消息: {result['message']}")
    #         return result['docid']  # 返回docid供后续使用
    #     else:
    #         print(f"✗ 文档创建失败: {result['message']}")
    #         print(f"  请求ID: {result['request_id']}")
    #         return None
    # except Exception as e:
    #     print(f"✗ 创建文档时发生异常: {e}")
    #     return None
    
    # 模拟返回结果
    print("\n模拟创建结果:")
    print("✓ 文档创建成功")
    print("  文档ID (docid): 4015215756")
    print("  请求ID: 61229add-ac0f-4899-93f0-84ad85eb6c9a")
    print("  消息: 文档创建成功")
    return 4015215756

def demo_append_document(document_id):
    """演示追加文档功能"""
    print("\n=== 追加文档内容演示 ===")
    
    client = IWikiClient()
    
    # 要追加的内容
    append_content = """

## 补充数据

### 内存使用情况
| 测试组 | 平均内存使用 | 峰值内存使用 | 内存泄漏检测 |
|--------|------------|------------|------------|
|实验组1|245MB|312MB|无|
|实验组2|248MB|315MB|无|
|对照组|252MB|325MB|轻微|

### 建议
1. 继续监控内存使用情况
2. 优化冷启动流程
3. 加强异常监控

---
*报告生成时间: 2025-01-01 12:00:00*
"""

    print(f"准备向文档 {document_id} 追加内容...")
    print("文档标题: 启动速度分析报告")
    
    # 注意：实际使用时取消注释以下代码
    # try:
    #     result = client.append_document(
    #         document_id=document_id,
    #         title="启动速度分析报告",
    #         content_to_append=append_content
    #     )
    #     
    #     print("\n追加结果:")
    #     if result['success']:
    #         print(f"✓ 内容追加成功")
    #         print(f"  请求ID: {result['request_id']}")
    #         print(f"  消息: {result['message']}")
    #     else:
    #         print(f"✗ 内容追加失败: {result['message']}")
    #         print(f"  请求ID: {result['request_id']}")
    # except Exception as e:
    #     print(f"✗ 追加内容时发生异常: {e}")
    
    # 模拟返回结果
    print("\n模拟追加结果:")
    print("✓ 内容追加成功")
    print("  请求ID: 55aca40d-1af3-40d1-ae32-f97a9b2b6fb0")
    print("  消息: 文档内容追加成功")

def main():
    """主函数"""
    print("IWiki客户端功能演示")
    print("=" * 50)
    
    # 演示创建文档
    docid = demo_create_document()
    
    # 演示追加文档内容
    if docid:
        demo_append_document(docid)
    
    print("\n" + "=" * 50)
    print("演示完成！")
    print("\n使用说明:")
    print("1. 取消代码中的注释即可实际调用API")
    print("2. create_document() 返回包含docid的解析结果")
    print("3. append_document() 返回包含成功状态的解析结果")
    print("4. 所有方法都包含完整的错误处理")

if __name__ == "__main__":
    main()
