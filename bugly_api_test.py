import requests

# 查询指定版本 crash、联网：https://iwiki.woa.com/p/4010805885#%E6%8C%87%E5%AE%9A%E4%BA%A7%E5%93%81%E7%89%88%E6%9C%AC-/-%E8%87%AA%E5%AE%9A%E4%B9%89%E9%85%8D%E7%BD%AE


url = 'http://api.apigw.oa.com/rmonitor/v1/sum'
headers = {
        "X-Gateway-Stage": "RELEASE",
        "X-Gateway-SecretId": "yyb_download_bugly_log",  # 申请的应用名
        "X-Gateway-SecretKey": "9b7bfd9e-80dd-4ddc-bd29-66b01aea",  # 应用名的密钥
        "X-ProductId": "900026256", # 产品ID
        "X-ProductKey": "Ty9B7jq98k6RNhdy", # 产品ID的key
        "Content-Type": "application/json"
    }

data = {
    "platform": "",
    "biz_type": "crash",
    "start_time": "2025-05-14T00:00:00+08:00",
    "end_time": "2025-06-04T00:00:00+08:00",
    "user_customs": [
        {
            "key":"product_version",
            "values":[
                "8.8.8_8883130_5742"
            ],
            "operator":"OR"
        }
    ]
}

response = requests.post(url, headers=headers, json=data)

if response.status_code == 200:
    result = response.json()
    print(f"Response data: {result}")
    base_rsp = result.get("base_rsp", {})
    code = base_rsp.get("code")
    msg = base_rsp.get("msg")
    print(f"Response code: {code}, message: {msg}")

    if code == 0:
        resp = result.get("response", {})
        product_id = resp.get('product_id')
        platform = resp.get('platform')
        print(f"Product ID: {product_id}")
        print(f"Platform: {platform}")

        data = resp.get('data', {})
        print(f"Product Version: {data.get('product_version')}")
        print(f"Affect Count: {data.get('affect_count')}")
        print(f"Affect User Count: {data.get('affect_user_count')}")
        print(f"Affect Device Count: {data.get('affect_device_count')}")
        print(f"Access Count: {data.get('access_count')}")
        print(f"Access User Count: {data.get('access_user_count')}")
        print(f"Access Device Count: {data.get('access_device_count')}")
        print(f"Launch Times: {data.get('launch_times')}")
    else:
        print("API returned error:", msg)
else:
    print("HTTP request failed with status code:", response.status_code)
