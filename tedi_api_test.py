import requests
import json

url = "http://api.apigw.oa.com/tedi-api/api/process/workflow-steps"

# 你的参数
params = {
    "processId": "592e405423d9f5e22c3ae118a47d19b6",
    "workflowId": "4b255ddf15af1d0fefee8b9932a3e0a7"
}

# 把参数转成json字符串，并作为param参数传递
payload = {
    "param": json.dumps(params)
}

response = requests.get(url, params=payload)

if response.status_code == 200:
    data = response.json()
    print("接口返回数据：", data)
else:
    print(f"请求失败，状态码：{response.status_code}")
