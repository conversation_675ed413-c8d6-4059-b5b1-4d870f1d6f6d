# {
#     "product_id": "d591c6d8f7",
#     "offset": 0,
#     "size": 1,
#     "cmd_type": 3,
#     "begin_time": "2025-03-09 00:00:00",
#     "end_time": "2025-03-10 23:59:59",
  
#   	"guid": "6b3e711e8c3ed35f7ba9db5e00001bc17a1f" // 过滤设备ID
# }

import requests
import json

# 通过 guid、时间 获取日志列表
api_url = "http://api.apigw.oa.com/rmonitor/v1/diagnose/query_log_upload_list" 


# 设置请求头
headers = {
    "X-Gateway-Stage": "RELEASE",
    "X-Gateway-SecretId": "yyb_download_bugly_log",  # 申请的应用名
    "X-Gateway-SecretKey": "9b7bfd9e-80dd-4ddc-bd29-66b01aea",  # 应用名的密钥
    "X-ProductId": "900026256", # 产品ID
    "X-ProductKey": "Ty9B7jq98k6RNhdy", # 产品ID的key
    "Content-Type": "application/json"
}

# 设置请求体
payload = {
    "product_id": "900026256",  # product_id
    "offset": 0,
    "size": 1,
    "cmd_type": 3,
    "begin_time": "2025-04-13 00:00:00",
    "end_time": "2025-04-14 23:59:59",
    "index_key": "2225114857285783808"  # guid
}

# 发送POST请求， 设置超时时间(秒)
response = requests.post(api_url, headers=headers, data=json.dumps(payload), timeout=60)  

# 检查响应状态
if response.status_code == 200:
    # 解析JSON响应
    response_data = response.json()
    print("日志列表:", json.dumps(response_data, indent=4, ensure_ascii=False))
else:
    print(f"请求失败，状态码: {response.status_code}, 错误信息: {response.text}")
# ("2025-04-13 00:00:00", "2025-04-14 23:59:59", "1838045804403135424")
