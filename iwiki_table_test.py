import requests
import hashlib
import time
import random
from urllib.parse import urlparse


def generate_signature(timestamp: str, nonce: str) -> str:
    """
    生成请求签名

    :param timestamp: 时间戳字符串
    :param nonce: 随机字符串
    :return: 大写的sha256签名字符串
    """
    raw_string = timestamp + '0PUOHJFWRP6NJY8ZCWEVKXY5N7CW5LSN' + nonce + timestamp
    signature = hashlib.sha256(raw_string.encode()).hexdigest().upper()
    return signature


url = 'http://api-idc.sgw.woa.com/ebus/iwiki/prod/tencent/api/vika/third/records'
params = {
    'doc_id': '4015162122',
    'pageNum': 1,
    'pageSize': 100,
    'viewId': 'viwgxtqw3mqD2'
}


timestamp = str(int(time.time()))
nonce = str(random.randint(1000, 9999))
signature = generate_signature(timestamp, nonce)

headers = {
    'Content-Type': 'application/json',
    'x-rio-paasid': 'yyb_ai',
    'x-rio-timestamp': timestamp,
    'x-rio-nonce': nonce,
    'x-rio-signature': signature
}


try:
    response = requests.get(url, params=params, headers=headers, timeout=10)
    response.raise_for_status()  # 如果响应状态码不是200，会抛出异常
    data = response.json()
    print(data)
except requests.exceptions.ConnectTimeout:
    print("连接超时，请检查网络或代理设置")
except requests.exceptions.HTTPError as err:
    print(f"HTTP错误: {err}")
except requests.exceptions.RequestException as err:
    print(f"请求异常: {err}")


# ======  新增文档 =======
# 参考文档：https://iwiki.woa.com/tencent/api/hook2/swagger-ui/#/v2%20-%20%E6%96%B0%E5%86%85%E5%AE%B9/post_v2_doc_create

body = """

## 启动速度

| 灰度新用户 | QUA |常规热启动<br>(gap<50ms) | 常规冷启动<br>(gap<100ms) | 常规外call热启动<br>(gap<50ms) | 常规外call冷启动<br>(gap<50ms) |
|--------|--------------|---------|------|------|-------|
|实验组1|TMAF_899_P_7921|11|22|33|44|
|实验组2|TMAF_899_P_7921|11|22|33|44|
|实验组3|TMAF_899_P_7921|11|22|33|44|
|对照组|TMAF_899_P_7921|11|22|33|44|


## crash率
<table border="1" cellspacing="0" cellpadding="5" style="border-collapse: collapse;">
  <thead>
    <tr>
      <th>灰度新用户</th>
      <th>QUA</th>
      <th>设备crash率<br>(<=0.18%)</th>
      <th>设备平均crash率<br>(<=2%)</th>
      <th>前台设备crash率<br>(<=0.06%)</th>
      <th>ANR率<br>(<=0.05%)</th>
      <th>灰度日期</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td rowspan="3">实验组1</td>
      <td rowspan="3">TMAF_899_P_7921</td>
      <td>11</td>
      <td>22</td>
      <td>33</td>
      <td>44</td>
      <td>2025/5/30</td>
    </tr>
    <tr>
      <td>11</td>
      <td>22</td>
      <td>33</td>
      <td>44</td>
      <td>2025/5/31</td>
    </tr>
    <tr>
      <td>11</td>
      <td>22</td>
      <td>33</td>
      <td>44</td>
      <td>2025/6/1</td>
    </tr>

    <tr>
      <td rowspan="3">实验组2</td>
      <td rowspan="3">TMAF_899_P_7851</td>
      <td>11</td>
      <td>22</td>
      <td>33</td>
      <td>44</td>
      <td>2025/5/30</td>
    </tr>
    <tr>
      <td>11</td>
      <td>22</td>
      <td>33</td>
      <td>44</td>
      <td>2025/5/31</td>
    </tr>
    <tr>
      <td>11</td>
      <td>22</td>
      <td>33</td>
      <td>44</td>
      <td>2025/6/1</td>
    </tr>

    <tr>
      <td rowspan="3">实验组3</td>
      <td rowspan="3">TMAF_899_P_7852</td>
      <td>11</td>
      <td>22</td>
      <td>33</td>
      <td>44</td>
      <td>2025/5/30</td>
    </tr>
    <tr>
      <td>11</td>
      <td>22</td>
      <td>33</td>
      <td>44</td>
      <td>2025/5/31</td>
    </tr>
    <tr>
      <td>11</td>
      <td>22</td>
      <td>33</td>
      <td>44</td>
      <td>2025/6/1</td>
    </tr>

    <tr>
      <td rowspan="3">对照组</td>
      <td rowspan="3">TMAF_899_P_7853</td>
      <td rowspan="3">11</td>
      <td rowspan="3">22</td>
      <td rowspan="3">33</td>
      <td rowspan="3">44</td>
      <td>2025/5/30</td>
    </tr>
    <tr>
      <td>2025/5/31</td>
    </tr>
    <tr>
      <td>2025/6/1</td>
    </tr>
  </tbody>
</table>

"""


url = 'http://api-idc.sgw.woa.com/ebus/iwiki/prod/tencent/api/v2/doc/create'
params = {
    "body": body,
    "body_mode": "",
    "contenttype": "MD",
    "spacekey": "ailogs",
    "title": "这是新文档标题",
    "parentid": 4015157988
}


timestamp = str(int(time.time()))
nonce = str(random.randint(1000, 9999))
signature = generate_signature(timestamp, nonce)

headers = {
    'Content-Type': 'application/json',
    'x-rio-paasid': 'yyb_ai',
    'x-rio-timestamp': timestamp,
    'x-rio-nonce': nonce,
    'x-rio-signature': signature
}


try:
    response = requests.post(url, headers=headers, json=params, timeout=10)
    # response = requests.get(url, params=params, headers=headers, timeout=10)
    response.raise_for_status()  # 如果响应状态码不是200，会抛出异常
    data = response.json()
    print(data)
except requests.exceptions.ConnectTimeout:
    print("连接超时，请检查网络或代理设置")
except requests.exceptions.HTTPError as err:
    print(f"HTTP错误: {err}")
except requests.exceptions.RequestException as err:
    print(f"请求异常: {err}")


#  ======  追加文档 =======

url = 'http://api-idc.sgw.woa.com/ebus/iwiki/prod/tencent/api/v2/doc/save/parts'
params = {
    "id": 4015180169,
    "title": "889版本灰度数据", # 固定文档 title 格式
    "after": body
}


timestamp = str(int(time.time()))
nonce = str(random.randint(1000, 9999))
signature = generate_signature(timestamp, nonce)

headers = {
    'Content-Type': 'application/json',
    'x-rio-paasid': 'yyb_ai',
    'x-rio-timestamp': timestamp,
    'x-rio-nonce': nonce,
    'x-rio-signature': signature
}


try:
    response = requests.post(url, headers=headers, json=params, timeout=10)
    # response = requests.get(url, params=params, headers=headers, timeout=10)
    response.raise_for_status()  # 如果响应状态码不是200，会抛出异常
    data = response.json()
    print(data)
except requests.exceptions.ConnectTimeout:
    print("连接超时，请检查网络或代理设置")
except requests.exceptions.HTTPError as err:
    print(f"HTTP错误: {err}")
except requests.exceptions.RequestException as err:
    print(f"请求异常: {err}")


# 删除字段
url = 'http://api-idc.sgw.woa.com/ebus/iwiki/prod/tencent/api/vika/third/fields'

params = {
    'doc_id': '4015299532',
    'fieldId': 'fldLoTGn9vHge'
}

response = requests.delete(url, params=params, headers=headers)

print(response.status_code)
print(response.json())  # 如果返回的是 JSON 格式
