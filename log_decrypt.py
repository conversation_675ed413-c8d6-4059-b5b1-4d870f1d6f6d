import requests
import json

# 通过检索Key获取日志列表 
api_url = "http://api.apigw.oa.com/rmonitor/v1/diagnose/decrypt_log" 
http://api.apigw.oa.com/rmonitor/v1/diagnose/query_log_upload_list_by_index

# 设置请求头
headers = {
    "X-Gateway-Stage": "RELEASE",
    "X-Gateway-SecretId": "yyb_download_bugly_log",  # 申请的应用名
    "X-Gateway-SecretKey": "9b7bfd9e-80dd-4ddc-bd29-66b01aea",  # 应用名的密钥
    "X-ProductId": "900026256", # 产品ID
    "X-ProductKey": "Ty9B7jq98k6RNhdy", # 产品ID的key
    "Content-Type": "application/json"
}

# 同步解密：
# {
#     "product_id": "49b1e9959d",
#     "log_url":"https://diagnose-1258344701.cos.ap-nanjing.myqcloud.com/log/49b1e9959d/2025-03-10/d898ee62f5134ac69aa3391e613d82c4/TDSLog_20250310_163421517_6b3e7_58047.zip?q-sign-algorithm=sha1&q-ak=AKIDPfjcFckswreJ2bGZq9cIm393mJbrBUbd&q-sign-time=1741596025%3B1741682425&q-key-time=1741596025%3B1741682425&q-header-list=host&q-url-param-list=&q-signature=9765a59ac2caac52c7b6e8d6bd61089d04d97489",
#     "sync":true
# }
# 异步解密：
# {
#     "cmd_type": 3,
#     "upload_id": "1182832180",
#     "product_id": "49b1e9959d"
# }
# 设置请求体
payload = {
    "product_id": "900026256",  # product_id
    "log_url": "",  # 未解密的日志链接
    "sync":true
}

# 发送POST请求， 设置超时时间(秒)
response = requests.post(api_url, headers=headers, data=json.dumps(payload), timeout=60)  

# 检查响应状态
if response.status_code == 200:
    # 解析JSON响应
    response_data = response.json()
    print("日志列表:", json.dumps(response_data, indent=4, ensure_ascii=False))
else:
    print(f"请求失败，状态码: {response.status_code}, 错误信息: {response.text}")
